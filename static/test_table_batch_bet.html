<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>按桌台整体保存批量下注测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .status-panel {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-connected {
            background-color: #28a745;
        }
        .status-disconnected {
            background-color: #dc3545;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            font-size: 14px;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        .btn-warning {
            background-color: #ffc107;
            color: #212529;
        }
        .btn-info {
            background-color: #17a2b8;
            color: white;
        }
        .section {
            margin-bottom: 30px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
        }
        .section h2 {
            margin-top: 0;
            color: #495057;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 10px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #495057;
        }
        .form-group input, .form-group select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
        }
        .messages {
            height: 400px;
            overflow-y: auto;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            background-color: #f8f9fa;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.4;
        }
        .message {
            margin-bottom: 10px;
            padding: 8px;
            border-radius: 4px;
            word-wrap: break-word;
        }
        .message-send {
            background-color: #e3f2fd;
            border-left: 4px solid #2196f3;
        }
        .message-receive {
            background-color: #f3e5f5;
            border-left: 4px solid #9c27b0;
        }
        .message-error {
            background-color: #ffebee;
            border-left: 4px solid #f44336;
        }
        .message-success {
            background-color: #e8f5e8;
            border-left: 4px solid #4caf50;
        }
        .timestamp {
            color: #666;
            font-size: 11px;
            margin-right: 10px;
        }
        .bet-record {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 10px;
        }
        .bet-record h4 {
            margin-top: 0;
            color: #495057;
        }
        .bet-fields {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
        }
        .add-bet-btn {
            background-color: #28a745;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
        }
        .remove-bet-btn {
            background-color: #dc3545;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
        }
        .stats {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        .stat-item {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            text-align: center;
            flex: 1;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 12px;
            color: #6c757d;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>按桌台整体保存批量下注测试</h1>
        
        <!-- 连接状态面板 -->
        <div class="status-panel">
            <div>
                <span class="status-indicator" id="statusIndicator"></span>
                <span id="statusText">未连接</span>
            </div>
            <div style="margin-top: 10px;">
                <button class="btn btn-primary" id="connectBtn" onclick="connect()">连接</button>
                <button class="btn btn-danger" id="disconnectBtn" onclick="disconnect()" disabled>断开</button>
            </div>
        </div>

        <!-- 统计面板 -->
        <div class="stats">
            <div class="stat-item">
                <div id="totalTests" class="stat-value">0</div>
                <div class="stat-label">总测试次数</div>
            </div>
            <div class="stat-item">
                <div id="successTests" class="stat-value">0</div>
                <div class="stat-label">成功次数</div>
            </div>
            <div class="stat-item">
                <div id="failedTests" class="stat-value">0</div>
                <div class="stat-label">失败次数</div>
            </div>
            <div class="stat-item">
                <div id="connectionDrops" class="stat-value">0</div>
                <div class="stat-label">连接中断</div>
            </div>
        </div>

        <!-- 控制面板 -->
        <div class="section">
            <h2>连接控制</h2>
            <button class="btn btn-success" onclick="login()">登录</button>
            <button class="btn btn-info" onclick="getTableInfo()">获取桌台信息</button>
            <button class="btn btn-warning" onclick="clearMessages()">清空消息</button>
        </div>

        <!-- 按桌台整体保存批量下注测试 -->
        <div class="section">
            <h2>按桌台整体保存批量下注测试</h2>
            <div class="form-group">
                <label>桌台ID:</label>
                <input type="number" id="tableId" value="1" min="1">
            </div>
            <div class="form-group">
                <label>账期:</label>
                <input type="text" id="accountPeriod" value="********" placeholder="YYYYMMDD">
            </div>
            <div class="form-group">
                <label>场次编号:</label>
                <input type="number" id="roundNo" value="1" min="1">
            </div>
            <div class="form-group">
                <label>局号编号:</label>
                <input type="number" id="handNo" value="1" min="1">
            </div>
            <div class="form-group">
                <label>记录数量:</label>
                <input type="number" id="recordCount" value="5" min="1" max="50">
            </div>
            
            <div id="betRecordsContainer">
                <div class="bet-record">
                    <h4>下注记录 #1</h4>
                    <div class="bet-fields">
                        <div class="form-group">
                            <label>洗码号:</label>
                            <input type="text" class="bet-wash-code" value="001" placeholder="如: 001">
                        </div>
                        <div class="form-group">
                            <label>客户姓名:</label>
                            <input type="text" class="bet-user-name" value="张三" placeholder="客户姓名">
                        </div>
                        <div class="form-group">
                            <label>货币类型:</label>
                            <select class="bet-currency-type">
                                <option value="1">筹码</option>
                                <option value="2">现金</option>
                                <option value="3">U码</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>庄金额:</label>
                            <input type="number" class="bet-banker-amount" value="1000" step="0.01" min="0">
                        </div>
                        <div class="form-group">
                            <label>闲金额:</label>
                            <input type="number" class="bet-player-amount" value="500" step="0.01" min="0">
                        </div>
                        <div class="form-group">
                            <label>和金额:</label>
                            <input type="number" class="bet-tie-amount" value="100" step="0.01" min="0">
                        </div>
                        <div class="form-group">
                            <label>庄对金额:</label>
                            <input type="number" class="bet-banker-pair-amount" value="50" step="0.01" min="0">
                        </div>
                        <div class="form-group">
                            <label>闲对金额:</label>
                            <input type="number" class="bet-player-pair-amount" value="50" step="0.01" min="0">
                        </div>
                    </div>
                </div>
            </div>
            
            <button class="add-bet-btn" onclick="addBetRecord()">添加下注记录</button>
            <button class="btn btn-success" onclick="testTableBatchBet()">测试按桌台整体保存批量下注</button>
            <button class="btn btn-info" onclick="testContinuousTableBatchBet()">连续测试</button>
            <button class="btn btn-warning" onclick="stopContinuousTest()">停止连续测试</button>
        </div>

        <!-- 消息日志 -->
        <div class="section">
            <h2>消息日志</h2>
            <div id="messages" class="messages"></div>
        </div>
    </div>

    <script>
        let ws = null;
        let isConnected = false;
        let isLoggedIn = false;
        let stats = {
            totalTests: 0,
            successTests: 0,
            failedTests: 0,
            connectionDrops: 0
        };
        let continuousTestInterval = null;

        function updateStatus(connected) {
            const status = document.getElementById('statusIndicator');
            const statusText = document.getElementById('statusText');
            const connectBtn = document.getElementById('connectBtn');
            const disconnectBtn = document.getElementById('disconnectBtn');
            
            isConnected = connected;
            if (connected) {
                status.className = 'status-indicator status-connected';
                statusText.textContent = 'WebSocket已连接';
                connectBtn.disabled = true;
                disconnectBtn.disabled = false;
            } else {
                status.className = 'status-indicator status-disconnected';
                statusText.textContent = 'WebSocket未连接';
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
                isLoggedIn = false;
            }
        }

        function updateStats() {
            document.getElementById('totalTests').textContent = stats.totalTests;
            document.getElementById('successTests').textContent = stats.successTests;
            document.getElementById('failedTests').textContent = stats.failedTests;
            document.getElementById('connectionDrops').textContent = stats.connectionDrops;
        }

        function addMessage(text, type = 'info') {
            const messages = document.getElementById('messages');
            const message = document.createElement('div');
            message.className = 'message message-' + type;
            message.innerHTML = '<span class="timestamp">' + new Date().toLocaleTimeString() + '</span> ' + text;
            messages.appendChild(message);
            messages.scrollTop = messages.scrollHeight;
        }

        function clearMessages() {
            document.getElementById('messages').innerHTML = '';
        }

        function connect() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                addMessage('WebSocket已连接', 'info');
                return;
            }

            ws = new WebSocket('ws://localhost:8080/ws');
            
            ws.onopen = function() {
                updateStatus(true);
                addMessage('WebSocket连接成功', 'success');
            };

            ws.onclose = function() {
                updateStatus(false);
                stats.connectionDrops++;
                updateStats();
                addMessage('WebSocket连接断开', 'error');
                
                // 自动重连
                setTimeout(() => {
                    if (!isConnected) {
                        addMessage('尝试重新连接...', 'info');
                        connect();
                    }
                }, 3000);
            };

            ws.onerror = function(error) {
                addMessage('WebSocket错误: ' + error, 'error');
            };

            ws.onmessage = function(event) {
                try {
                    const message = JSON.parse(event.data);
                    handleMessage(message);
                } catch (error) {
                    addMessage('消息解析错误: ' + error, 'error');
                }
            };
        }

        function disconnect() {
            if (ws) {
                ws.close();
                ws = null;
            }
            updateStatus(false);
            addMessage('手动断开连接', 'info');
        }

        function login() {
            if (!isConnected) {
                addMessage('WebSocket未连接', 'error');
                return;
            }

            sendMessage({
                type: 'login',
                data: {
                    username: 'admin',
                    password: '123456'
                }
            });
        }

        function handleMessage(message) {
            switch (message.type) {
                case 'login_success':
                    isLoggedIn = true;
                    addMessage('登录成功', 'success');
                    break;
                case 'login_error':
                    addMessage('登录失败: ' + message.data.error, 'error');
                    break;
                case 'table_batch_bet_entry_success':
                    stats.successTests++;
                    updateStats();
                    addMessage('按桌台整体保存批量下注成功: ' + message.data.message, 'success');
                    break;
                case 'table_batch_bet_entry_error':
                    stats.failedTests++;
                    updateStats();
                    addMessage('按桌台整体保存批量下注失败: ' + message.data.error, 'error');
                    break;
                default:
                    addMessage('收到消息: ' + message.type, 'info');
            }
        }

        function sendMessage(message) {
            if (!isConnected) {
                addMessage('WebSocket未连接', 'error');
                return;
            }
            ws.send(JSON.stringify(message));
        }

        function getTableInfo() {
            if (!isConnected) {
                addMessage('WebSocket未连接', 'error');
                return;
            }

            sendMessage({
                type: 'get_table_info'
            });
        }

        function addBetRecord() {
            const container = document.getElementById('betRecordsContainer');
            const recordCount = container.children.length + 1;
            
            const recordDiv = document.createElement('div');
            recordDiv.className = 'bet-record';
            recordDiv.innerHTML = `
                <h4>下注记录 #${recordCount}</h4>
                <div class="bet-fields">
                    <div class="form-group">
                        <label>洗码号:</label>
                        <input type="text" class="bet-wash-code" value="${String(recordCount).padStart(3, '0')}" placeholder="如: 001">
                    </div>
                    <div class="form-group">
                        <label>客户姓名:</label>
                        <input type="text" class="bet-user-name" value="客户${recordCount}" placeholder="客户姓名">
                    </div>
                    <div class="form-group">
                        <label>货币类型:</label>
                        <select class="bet-currency-type">
                            <option value="1">筹码</option>
                            <option value="2">现金</option>
                            <option value="3">U码</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>庄金额:</label>
                        <input type="number" class="bet-banker-amount" value="${Math.floor(Math.random() * 1000) + 100}" step="0.01" min="0">
                    </div>
                    <div class="form-group">
                        <label>闲金额:</label>
                        <input type="number" class="bet-player-amount" value="${Math.floor(Math.random() * 500) + 50}" step="0.01" min="0">
                    </div>
                    <div class="form-group">
                        <label>和金额:</label>
                        <input type="number" class="bet-tie-amount" value="${Math.floor(Math.random() * 200) + 10}" step="0.01" min="0">
                    </div>
                    <div class="form-group">
                        <label>庄对金额:</label>
                        <input type="number" class="bet-banker-pair-amount" value="${Math.floor(Math.random() * 100) + 10}" step="0.01" min="0">
                    </div>
                    <div class="form-group">
                        <label>闲对金额:</label>
                        <input type="number" class="bet-player-pair-amount" value="${Math.floor(Math.random() * 100) + 10}" step="0.01" min="0">
                    </div>
                </div>
                <button class="remove-bet-btn" onclick="removeBetRecord(this)">删除</button>
            `;
            
            container.appendChild(recordDiv);
        }

        function removeBetRecord(button) {
            const container = document.getElementById('betRecordsContainer');
            if (container.children.length > 1) {
                button.parentElement.remove();
            }
        }

        function generateTableBetRecords() {
            const tableId = parseInt(document.getElementById('tableId').value);
            const accountPeriod = document.getElementById('accountPeriod').value;
            const roundNo = parseInt(document.getElementById('roundNo').value);
            const handNo = parseInt(document.getElementById('handNo').value);
            const recordCount = parseInt(document.getElementById('recordCount').value);

            const betRecords = [];
            const container = document.getElementById('betRecordsContainer');
            const recordDivs = container.querySelectorAll('.bet-record');

            for (let i = 0; i < Math.min(recordCount, recordDivs.length); i++) {
                const div = recordDivs[i];
                const washCode = div.querySelector('.bet-wash-code').value;
                const userName = div.querySelector('.bet-user-name').value;
                const currencyType = parseInt(div.querySelector('.bet-currency-type').value);
                const bankerAmount = parseFloat(div.querySelector('.bet-banker-amount').value);
                const playerAmount = parseFloat(div.querySelector('.bet-player-amount').value);
                const tieAmount = parseFloat(div.querySelector('.bet-tie-amount').value);
                const bankerPairAmount = parseFloat(div.querySelector('.bet-banker-pair-amount').value);
                const playerPairAmount = parseFloat(div.querySelector('.bet-player-pair-amount').value);

                betRecords.push({
                    table_id: tableId,
                    game_type: 1,
                    account_period: accountPeriod,
                    round_no: roundNo,
                    hand_no: handNo,
                    wash_code: washCode,
                    user_name: userName,
                    currency_type: currencyType,
                    banker_amount: bankerAmount,
                    player_amount: playerAmount,
                    tie_amount: tieAmount,
                    banker_pair_amount: bankerPairAmount,
                    player_pair_amount: playerPairAmount,
                    lucky_6_amount: 0,
                    lucky_7_amount: 0,
                    win_result: "",
                    win_loss: 0,
                    loss: 0,
                    amount_tip: 0,
                    amount_bottom: 0,
                    wash_rate: 0.00018,
                    wash_amount: bankerAmount + playerAmount + tieAmount + bankerPairAmount + playerPairAmount,
                    wash_tip: (bankerAmount + playerAmount + tieAmount + bankerPairAmount + playerPairAmount) * 0.00018
                });
            }

            return {
                table_id: tableId,
                account_period: accountPeriod,
                round_no: roundNo,
                hand_no: handNo,
                bet_records: betRecords
            };
        }

        function testTableBatchBet() {
            if (!isLoggedIn) {
                addMessage('请先登录', 'error');
                return;
            }

            const tableBetRecord = generateTableBetRecords();
            
            stats.totalTests++;
            updateStats();
            
            addMessage(`开始测试按桌台整体保存批量下注，桌台ID: ${tableBetRecord.table_id}, 账期: ${tableBetRecord.account_period}, 场次: ${tableBetRecord.round_no}, 局数: ${tableBetRecord.hand_no}, 记录数量: ${tableBetRecord.bet_records.length}`, 'info');
            
            sendMessage({
                type: 'table_batch_bet_entry',
                data: {
                    table_bet_record: tableBetRecord
                }
            });
        }

        function testContinuousTableBatchBet() {
            if (continuousTestInterval) {
                addMessage('连续测试已在运行', 'info');
                return;
            }

            addMessage('开始连续测试，每5秒执行一次按桌台整体保存批量下注', 'info');
            
            continuousTestInterval = setInterval(() => {
                if (isLoggedIn) {
                    testTableBatchBet();
                } else {
                    addMessage('登录状态丢失，尝试重新登录', 'info');
                    login();
                }
            }, 5000);
        }

        function stopContinuousTest() {
            if (continuousTestInterval) {
                clearInterval(continuousTestInterval);
                continuousTestInterval = null;
                addMessage('停止连续测试', 'info');
            }
        }

        // 页面加载时自动连接
        window.onload = function() {
            connect();
        };

        // 页面卸载时清理
        window.onbeforeunload = function() {
            if (continuousTestInterval) {
                clearInterval(continuousTestInterval);
            }
            if (ws) {
                ws.close();
            }
        };
    </script>
</body>
</html> 