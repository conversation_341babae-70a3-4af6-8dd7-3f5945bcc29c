<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>查询下注记录接口测试工具</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 30px;
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .status-panel {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-connected {
            background-color: #28a745;
        }
        .status-disconnected {
            background-color: #dc3545;
        }
        .control-panel {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-primary:hover {
            background-color: #0056b3;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-success:hover {
            background-color: #1e7e34;
        }
        .btn-warning {
            background-color: #ffc107;
            color: #212529;
        }
        .btn-warning:hover {
            background-color: #e0a800;
        }
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        .btn-danger:hover {
            background-color: #c82333;
        }
        .btn-info {
            background-color: #17a2b8;
            color: white;
        }
        .btn-info:hover {
            background-color: #138496;
        }
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        .section {
            margin-bottom: 30px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
        }
        .section h2 {
            margin-top: 0;
            color: #495057;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 10px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #495057;
        }
        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        .form-group input:focus, .form-group select:focus, .form-group textarea:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
        }
        .form-inline {
            display: flex;
            gap: 10px;
            align-items: end;
        }
        .form-inline .form-group {
            flex: 1;
            margin-bottom: 0;
        }
        .messages {
            height: 400px;
            overflow-y: auto;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            background-color: #f8f9fa;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.4;
        }
        .message {
            margin-bottom: 10px;
            padding: 8px;
            border-radius: 4px;
            word-wrap: break-word;
        }
        .message-send {
            background-color: #e3f2fd;
            border-left: 4px solid #2196f3;
        }
        .message-receive {
            background-color: #f3e5f5;
            border-left: 4px solid #9c27b0;
        }
        .message-error {
            background-color: #ffebee;
            border-left: 4px solid #f44336;
        }
        .message-success {
            background-color: #e8f5e8;
            border-left: 4px solid #4caf50;
        }
        .timestamp {
            color: #666;
            font-size: 11px;
            margin-right: 10px;
        }
        .records-display {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
        }
        .record-item {
            background-color: white;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 15px;
        }
        .record-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e9ecef;
        }
        .record-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        .record-field {
            display: flex;
            justify-content: space-between;
        }
        .record-field-label {
            font-weight: bold;
            color: #495057;
        }
        .record-field-value {
            color: #6c757d;
        }
        .currency-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        .currency-chips {
            background-color: #d4edda;
            color: #155724;
        }
        .currency-cash {
            background-color: #d1ecf1;
            color: #0c5460;
        }
        .currency-u {
            background-color: #ffeaa7;
            color: #856404;
        }
        .totals-panel {
            background-color: #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
        }
        .totals-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        .total-item {
            background-color: white;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
        }
        .total-label {
            font-weight: bold;
            color: #495057;
            margin-bottom: 5px;
        }
        .total-value {
            font-size: 18px;
            color: #007bff;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>查询下注记录接口测试工具</h1>
        
        <!-- 连接状态面板 -->
        <div class="status-panel">
            <h3>连接状态</h3>
            <div>
                <span class="status-indicator" id="status-indicator"></span>
                <span id="connection-status">未连接</span>
                <span style="margin-left: 20px;">认证状态: <span id="auth-status">未登录</span></span>
            </div>
        </div>

        <!-- 控制面板 -->
        <div class="control-panel">
            <button class="btn btn-primary" onclick="connect()">连接</button>
            <button class="btn btn-danger" onclick="disconnect()">断开连接</button>
            <button class="btn btn-success" onclick="login()">登录</button>
            <button class="btn btn-warning" onclick="clearMessages()">清空日志</button>
        </div>

        <!-- 查询下注记录区域 -->
        <div class="section">
            <h2>查询Redis中的下注记录</h2>
            <p style="color: #6c757d; margin-bottom: 20px;">
                通过账期、场次编号、局号编号查询当前桌台在Redis中的所有下注记录。系统会自动根据客户端IP获取桌台信息。
            </p>
            
            <div class="form-inline">
                <div class="form-group">
                    <label for="accountPeriod">账期</label>
                    <input type="text" id="accountPeriod" placeholder="格式: YYYYMMDD, 如: ********" value="********">
                </div>
                <div class="form-group">
                    <label for="roundNo">场次编号</label>
                    <input type="number" id="roundNo" placeholder="场次编号" value="1" min="1">
                </div>
                <div class="form-group">
                    <label for="handNo">局号编号</label>
                    <input type="number" id="handNo" placeholder="局号编号" value="1" min="1">
                </div>
                <div class="form-group">
                    <button class="btn btn-info" onclick="queryBetRecords()">查询下注记录</button>
                </div>
            </div>

            <div class="records-display" id="recordsDisplay" style="display: none;">
                <h3>查询结果</h3>
                <div id="queryResults"></div>
            </div>
        </div>

        <!-- 消息日志区域 -->
        <div class="section">
            <h2>消息日志</h2>
            <div class="messages" id="messages"></div>
        </div>
    </div>

    <script>
        let ws = null;
        let isAuthenticated = false;

        function getCurrentTime() {
            return new Date().toLocaleTimeString('zh-CN', { hour12: false });
        }

        function addMessage(content, type = 'info') {
            const messages = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message message-${type}`;
            
            const timestamp = document.createElement('span');
            timestamp.className = 'timestamp';
            timestamp.textContent = getCurrentTime();
            
            const contentSpan = document.createElement('span');
            contentSpan.textContent = content;
            
            messageDiv.appendChild(timestamp);
            messageDiv.appendChild(contentSpan);
            messages.appendChild(messageDiv);
            messages.scrollTop = messages.scrollHeight;
        }

        function updateConnectionStatus(connected) {
            const indicator = document.getElementById('status-indicator');
            const status = document.getElementById('connection-status');
            
            if (connected) {
                indicator.className = 'status-indicator status-connected';
                status.textContent = '已连接';
            } else {
                indicator.className = 'status-indicator status-disconnected';
                status.textContent = '未连接';
                updateAuthStatus(false);
            }
        }

        function updateAuthStatus(authenticated) {
            isAuthenticated = authenticated;
            const authStatus = document.getElementById('auth-status');
            authStatus.textContent = authenticated ? '已登录' : '未登录';
            authStatus.style.color = authenticated ? '#28a745' : '#dc3545';
        }

        function connect() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                addMessage('WebSocket已经连接', 'info');
                return;
            }

            ws = new WebSocket('ws://localhost:8080/ws');
            
            ws.onopen = function(event) {
                addMessage('WebSocket连接成功', 'success');
                updateConnectionStatus(true);
            };
            
            ws.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);
                    addMessage('收到: ' + JSON.stringify(data, null, 2), 'receive');
                    
                    // 处理特定消息类型
                    if (data.type === 'login_success') {
                        updateAuthStatus(true);
                    } else if (data.type === 'logout_success') {
                        updateAuthStatus(false);
                    } else if (data.type === 'query_bet_records_success') {
                        displayQueryResults(data.data);
                    } else if (data.type === 'query_bet_records_error') {
                        addMessage('查询失败: ' + data.data.error, 'error');
                    }
                } catch (e) {
                    addMessage('收到: ' + event.data, 'receive');
                }
            };
            
            ws.onclose = function(event) {
                addMessage('WebSocket连接已关闭', 'error');
                updateConnectionStatus(false);
            };
            
            ws.onerror = function(error) {
                addMessage('WebSocket错误: ' + error, 'error');
                updateConnectionStatus(false);
            };
        }

        function disconnect() {
            if (ws) {
                ws.close();
                ws = null;
                addMessage('主动断开连接', 'info');
                updateConnectionStatus(false);
            }
        }

        function sendMessage(message) {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                addMessage('WebSocket未连接，无法发送消息', 'error');
                return false;
            }
            
            ws.send(JSON.stringify(message));
            addMessage('发送: ' + JSON.stringify(message, null, 2), 'send');
            return true;
        }

        function login() {
            const message = {
                type: 'login',
                data: {
                    username: 'admin',
                    password: '123456'
                }
            };
            sendMessage(message);
        }

        function queryBetRecords() {
            if (!isAuthenticated) {
                addMessage('请先登录', 'error');
                return;
            }

            const accountPeriod = document.getElementById('accountPeriod').value.trim();
            const roundNo = parseInt(document.getElementById('roundNo').value);
            const handNo = parseInt(document.getElementById('handNo').value);

            if (!accountPeriod) {
                addMessage('请填写账期', 'error');
                return;
            }

            if (!roundNo || roundNo < 1) {
                addMessage('场次编号必须大于0', 'error');
                return;
            }

            if (!handNo || handNo < 1) {
                addMessage('局号编号必须大于0', 'error');
                return;
            }

            // 验证账期格式
            if (!/^\d{8}$/.test(accountPeriod)) {
                addMessage('账期格式错误，请使用YYYYMMDD格式，如：********', 'error');
                return;
            }

            const message = {
                type: 'query_bet_records',
                data: {
                    account_period: accountPeriod,
                    round_no: roundNo,
                    hand_no: handNo
                }
            };

            if (sendMessage(message)) {
                document.getElementById('recordsDisplay').style.display = 'none';
                addMessage(`正在查询账期${accountPeriod}、场次${roundNo}、局号${handNo}的下注记录...`, 'info');
            }
        }

        function displayQueryResults(data) {
            const recordsDisplay = document.getElementById('recordsDisplay');
            const queryResults = document.getElementById('queryResults');
            
            recordsDisplay.style.display = 'block';
            
            let html = `
                <div style="margin-bottom: 20px;">
                    <h4>桌台信息</h4>
                    <p><strong>桌台ID:</strong> ${data.table_id}</p>
                    <p><strong>桌台名称:</strong> ${data.table_name}</p>
                    <p><strong>账期:</strong> ${data.account_period}</p>
                    <p><strong>场次编号:</strong> ${data.round_no}</p>
                    <p><strong>局号编号:</strong> ${data.hand_no}</p>
                    <p><strong>记录数量:</strong> ${data.record_count} 条</p>
                </div>
            `;

            if (data.record_count > 0) {
                html += '<h4>下注记录列表</h4>';
                
                data.bet_records.forEach((record, index) => {
                    const currencyClass = record.currency_type === 1 ? 'currency-chips' : 
                                        record.currency_type === 2 ? 'currency-cash' : 'currency-u';
                    
                    html += `
                        <div class="record-item">
                            <div class="record-header">
                                <h5>记录 ${index + 1}: ${record.user_name || '未知用户'} (${record.wash_code})</h5>
                                <span class="currency-badge ${currencyClass}">${record.currency_type_name}</span>
                            </div>
                            <div class="record-details">
                                <div class="record-field">
                                    <span class="record-field-label">庄金额:</span>
                                    <span class="record-field-value">${record.banker_amount}</span>
                                </div>
                                <div class="record-field">
                                    <span class="record-field-label">闲金额:</span>
                                    <span class="record-field-value">${record.player_amount}</span>
                                </div>
                                <div class="record-field">
                                    <span class="record-field-label">和金额:</span>
                                    <span class="record-field-value">${record.tie_amount}</span>
                                </div>
                                <div class="record-field">
                                    <span class="record-field-label">庄对金额:</span>
                                    <span class="record-field-value">${record.banker_pair_amount}</span>
                                </div>
                                <div class="record-field">
                                    <span class="record-field-label">闲对金额:</span>
                                    <span class="record-field-value">${record.player_pair_amount}</span>
                                </div>
                                <div class="record-field">
                                    <span class="record-field-label">幸运6金额:</span>
                                    <span class="record-field-value">${record.lucky_6_amount}</span>
                                </div>
                                <div class="record-field">
                                    <span class="record-field-label">幸运7金额:</span>
                                    <span class="record-field-value">${record.lucky_7_amount}</span>
                                </div>
                                <div class="record-field">
                                    <span class="record-field-label">总下注金额:</span>
                                    <span class="record-field-value"><strong>${record.total_bet_amount}</strong></span>
                                </div>
                                <div class="record-field">
                                    <span class="record-field-label">结果状态:</span>
                                    <span class="record-field-value">${record.win_result}</span>
                                </div>
                                <div class="record-field">
                                    <span class="record-field-label">洗码率:</span>
                                    <span class="record-field-value">${record.wash_rate}</span>
                                </div>
                            </div>
                        </div>
                    `;
                });

                // 显示统计信息
                if (data.totals_by_status && Object.keys(data.totals_by_status).length > 0) {
                    html += `
                        <div class="totals-panel">
                            <h4>统计信息</h4>
                            <div class="totals-grid">
                    `;
                    
                    for (const [key, value] of Object.entries(data.totals_by_status)) {
                        html += `
                            <div class="total-item">
                                <div class="total-label">${key}</div>
                                <div class="total-value">${value}</div>
                            </div>
                        `;
                    }
                    
                    html += '</div></div>';
                }
            } else {
                html += '<p style="color: #6c757d; font-style: italic;">该局暂无下注记录</p>';
            }

            queryResults.innerHTML = html;
        }

        function clearMessages() {
            document.getElementById('messages').innerHTML = '';
        }

        // 页面加载时初始化
        window.onload = function() {
            updateConnectionStatus(false);
            addMessage('页面加载完成，点击"连接"按钮开始测试', 'info');
        };
    </script>
</body>
</html> 