<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IP地址测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .messages {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .message {
            margin: 5px 0;
            padding: 5px;
            border-radius: 3px;
        }
        .message.info { background-color: #e7f3ff; }
        .message.error { background-color: #ffe6e6; }
        .message.success { background-color: #e6ffe6; }
    </style>
</head>
<body>
    <div class="container">
        <h1>IP地址测试页面</h1>
        
        <div class="section">
            <h2>连接信息</h2>
            <button class="btn" id="connectBtn">连接WebSocket</button>
            <button class="btn" id="disconnectBtn" disabled>断开连接</button>
            <div id="connectionStatus" class="info">未连接</div>
        </div>

        <div class="section">
            <h2>IP地址测试</h2>
            <button class="btn" id="getTableInfoBtn" disabled>获取桌台信息</button>
            <button class="btn" id="getLatestTablesStartBtn" disabled>获取最新开台数据</button>
        </div>

        <div class="section">
            <h2>访问方式说明</h2>
            <div class="info">
                <strong>正确的访问方式：</strong><br>
                ✅ 使用实际IP地址：<code>http://************:8080/static/test_ip.html</code><br>
                ❌ 不要使用：<code>http://localhost:8080/static/test_ip.html</code> 或 <code>http://127.0.0.1:8080/static/test_ip.html</code>
            </div>
            <div class="info">
                <strong>当前访问地址：</strong><br>
                <span id="currentUrl"></span>
            </div>
        </div>

        <div class="section">
            <h2>消息日志</h2>
            <div class="messages" id="messages"></div>
        </div>

        <div id="tableInfoSection" class="section" style="display: none;">
            <h2>桌台信息</h2>
            <div id="tableDetails"></div>
        </div>
    </div>

    <script>
        let ws = null;
        let clientId = null;

        // 显示当前访问地址
        document.getElementById('currentUrl').textContent = window.location.href;

        // 添加消息到日志
        function addMessage(message, type = 'info') {
            const messages = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            messages.appendChild(messageDiv);
            messages.scrollTop = messages.scrollHeight;
        }

        // 连接WebSocket
        function connect() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const host = window.location.host;
            const wsUrl = `${protocol}//${host}/ws`;
            
            addMessage(`正在连接到: ${wsUrl}`, 'info');
            
            ws = new WebSocket(wsUrl);
            
            ws.onopen = function() {
                addMessage('WebSocket连接已建立', 'success');
                document.getElementById('connectionStatus').textContent = '已连接';
                document.getElementById('connectionStatus').className = 'success';
                document.getElementById('connectBtn').disabled = true;
                document.getElementById('disconnectBtn').disabled = false;
                document.getElementById('getTableInfoBtn').disabled = false;
                document.getElementById('getLatestTablesStartBtn').disabled = false;
            };
            
            ws.onmessage = function(event) {
                const message = JSON.parse(event.data);
                addMessage(`收到消息: ${JSON.stringify(message, null, 2)}`, 'info');
                
                switch(message.type) {
                    case 'connection':
                        clientId = message.data.id;
                        addMessage(`客户端ID: ${clientId}`, 'success');
                        break;
                        
                    case 'table_info_success':
                        displayTableInfo(message.data.table);
                        addMessage('桌台信息获取成功！', 'success');
                        break;
                        
                    case 'table_info_error':
                        addMessage(`桌台信息获取失败: ${message.data.error}`, 'error');
                        addMessage(`客户端IP: ${message.data.client_ip}`, 'info');
                        break;
                        
                    case 'get_latest_tables_start_success':
                        addMessage('最新开台数据获取成功！', 'success');
                        addMessage(`客户端IP: ${message.data.client_ip}`, 'info');
                        break;
                        
                    case 'get_latest_tables_start_error':
                        addMessage(`最新开台数据获取失败: ${message.data.error}`, 'error');
                        addMessage(`客户端IP: ${message.data.client_ip}`, 'info');
                        break;
                }
            };
            
            ws.onclose = function() {
                addMessage('WebSocket连接已关闭', 'error');
                document.getElementById('connectionStatus').textContent = '已断开';
                document.getElementById('connectionStatus').className = 'error';
                document.getElementById('connectBtn').disabled = false;
                document.getElementById('disconnectBtn').disabled = true;
                document.getElementById('getTableInfoBtn').disabled = true;
                document.getElementById('getLatestTablesStartBtn').disabled = true;
            };
            
            ws.onerror = function(error) {
                addMessage(`WebSocket错误: ${error}`, 'error');
            };
        }

        // 断开连接
        function disconnect() {
            if (ws) {
                ws.close();
            }
        }

        // 发送消息
        function sendMessage(message) {
            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify(message));
                addMessage(`发送消息: ${JSON.stringify(message)}`, 'info');
            } else {
                addMessage('WebSocket未连接', 'error');
            }
        }

        // 获取桌台信息
        function getTableInfo() {
            sendMessage({
                type: 'get_table_info'
            });
        }

        // 获取最新开台数据
        function getLatestTablesStart() {
            sendMessage({
                type: 'get_latest_tables_start'
            });
        }

        // 显示桌台信息
        function displayTableInfo(tableInfo) {
            const tableDetails = document.getElementById('tableDetails');
            const tableInfoSection = document.getElementById('tableInfoSection');
            
            tableDetails.innerHTML = `
                <div class="info">
                    <strong>桌台编号:</strong> ${tableInfo.table_code}<br>
                    <strong>桌台名称:</strong> ${tableInfo.table_name}<br>
                    <strong>游戏类型:</strong> ${tableInfo.game_type_name}<br>
                    <strong>桌台IP:</strong> ${tableInfo.table_ip}<br>
                    <strong>视频地址:</strong> ${tableInfo.video_url}<br>
                    <strong>业务通道:</strong> ${tableInfo.channel_name}<br>
                    <strong>洗码率:</strong> ${tableInfo.wash_rate}<br>
                    <strong>状态:</strong> ${tableInfo.status_name}<br>
                    <strong>创建时间:</strong> ${tableInfo.create_time}
                </div>
            `;
            
            tableInfoSection.style.display = 'block';
        }

        // 事件监听
        document.getElementById('connectBtn').addEventListener('click', connect);
        document.getElementById('disconnectBtn').addEventListener('click', disconnect);
        document.getElementById('getTableInfoBtn').addEventListener('click', getTableInfo);
        document.getElementById('getLatestTablesStartBtn').addEventListener('click', getLatestTablesStart);

        // 页面加载时显示说明
        addMessage('请使用正确的IP地址访问此页面', 'info');
        addMessage('如果看到 ::1 或 127.0.0.1，说明您使用了localhost访问', 'error');
        addMessage('请使用 http://************:8080/static/test_ip.html 访问', 'success');
    </script>
</body>
</html> 