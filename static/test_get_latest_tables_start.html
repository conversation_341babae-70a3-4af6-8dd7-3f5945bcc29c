<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>获取最新开台数据 - 测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        .status.connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.authenticated {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .messages {
            height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background-color: #f8f9fa;
            font-family: monospace;
            font-size: 12px;
        }
        .message {
            margin-bottom: 10px;
            padding: 5px;
            border-radius: 3px;
        }
        .message.send {
            background-color: #e3f2fd;
            border-left: 3px solid #2196f3;
        }
        .message.receive {
            background-color: #f3e5f5;
            border-left: 3px solid #9c27b0;
        }
        .message.success {
            background-color: #e8f5e8;
            border-left: 3px solid #4caf50;
        }
        .message.error {
            background-color: #ffebee;
            border-left: 3px solid #f44336;
        }
        .result-display {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
        }
        .result-display h4 {
            margin-top: 0;
            color: #495057;
        }
        .result-display pre {
            background-color: #ffffff;
            border: 1px solid #dee2e6;
            border-radius: 3px;
            padding: 10px;
            overflow-x: auto;
            margin: 0;
        }
        .two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        @media (max-width: 768px) {
            .two-column {
                grid-template-columns: 1fr;
            }
        }
        
        .tables-start-info {
            background-color: #e8f5e8;
            border: 1px solid #c3e6c3;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 15px;
        }
        
        .tables-start-info h4 {
            margin-top: 0;
            color: #2e7d32;
        }
        
        .info-row {
            margin-bottom: 8px;
            padding: 5px;
        }
        
        .info-row.tables-name {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 3px;
            font-weight: bold;
        }
        
        .raw-json {
            margin-top: 15px;
        }
        
        .raw-json h4 {
            color: #495057;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>获取最新开台数据 - 测试页面</h1>

        <!-- 连接状态 -->
        <div class="section">
            <h3>连接状态</h3>
            <div id="connectionStatus" class="status disconnected">未连接</div>
            <div id="authStatus" class="status disconnected">未认证</div>
            <button id="connectBtn">连接WebSocket</button>
            <button id="disconnectBtn" disabled>断开连接</button>
        </div>

        <!-- 用户登录 -->
        <div class="section">
            <h3>用户登录</h3>
            <div class="form-group">
                <label for="username">用户名:</label>
                <input type="text" id="username" value="admin">
            </div>
            <div class="form-group">
                <label for="password">密码:</label>
                <input type="password" id="password" value="123456">
            </div>
            <button id="loginBtn" disabled>登录</button>
            <button id="logoutBtn" disabled>登出</button>
        </div>

        <!-- 查询最新开台数据 -->
        <div class="section">
            <h3>获取最新开台数据</h3>
            <div class="form-group">
                <p><strong>说明：</strong>此接口会自动根据客户端IP获取对应的桌台信息，然后查询该桌台的最新开台数据</p>
            </div>
            <button id="getLatestTablesStartBtn" disabled>获取最新开台数据</button>

            <div id="tablesStartResult" class="result-display" style="display: none;">
                <h4>查询结果</h4>
                <pre id="tablesStartData"></pre>
            </div>
        </div>

        <!-- 消息日志 -->
        <div class="section">
            <h3>消息日志</h3>
            <button id="clearBtn">清空日志</button>
            <div id="messages" class="messages"></div>
        </div>
    </div>

    <script>
        let ws = null;
        let isAuthenticated = false;

        // DOM 元素
        const connectBtn = document.getElementById('connectBtn');
        const disconnectBtn = document.getElementById('disconnectBtn');
        const loginBtn = document.getElementById('loginBtn');
        const logoutBtn = document.getElementById('logoutBtn');
        const getLatestTablesStartBtn = document.getElementById('getLatestTablesStartBtn');
        const clearBtn = document.getElementById('clearBtn');
        const messages = document.getElementById('messages');
        const connectionStatus = document.getElementById('connectionStatus');
        const authStatus = document.getElementById('authStatus');
        const tablesStartResult = document.getElementById('tablesStartResult');
        const tablesStartData = document.getElementById('tablesStartData');

        // 事件监听器
        connectBtn.addEventListener('click', connect);
        disconnectBtn.addEventListener('click', disconnect);
        loginBtn.addEventListener('click', login);
        logoutBtn.addEventListener('click', logout);
        getLatestTablesStartBtn.addEventListener('click', getLatestTablesStart);
        clearBtn.addEventListener('click', clearMessages);

        // 更新连接状态
        function updateConnectionStatus(connected) {
            if (connected) {
                connectionStatus.textContent = '已连接';
                connectionStatus.className = 'status connected';
                connectBtn.disabled = true;
                disconnectBtn.disabled = false;
                loginBtn.disabled = false;
            } else {
                connectionStatus.textContent = '未连接';
                connectionStatus.className = 'status disconnected';
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
                loginBtn.disabled = true;
                updateAuthStatus(false);
            }
        }

        // 更新认证状态
        function updateAuthStatus(authenticated) {
            isAuthenticated = authenticated;
            if (authenticated) {
                authStatus.textContent = '已认证';
                authStatus.className = 'status authenticated';
                loginBtn.disabled = true;
                logoutBtn.disabled = false;
                getLatestTablesStartBtn.disabled = false;
            } else {
                authStatus.textContent = '未认证';
                authStatus.className = 'status disconnected';
                loginBtn.disabled = false;
                logoutBtn.disabled = true;
                getLatestTablesStartBtn.disabled = true;
            }
        }

        // 添加消息到日志
        function addMessage(message, type = 'info') {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong><br>${message}`;
            messages.appendChild(messageDiv);
            messages.scrollTop = messages.scrollHeight;
        }

        // 连接WebSocket
        function connect() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                return;
            }

            ws = new WebSocket('ws://192.168.1.12:8080/ws');

            ws.onopen = function() {
                updateConnectionStatus(true);
                addMessage('已连接到WebSocket服务器', 'success');
            };

            ws.onmessage = function(event) {
                const message = JSON.parse(event.data);
                addMessage('收到消息: ' + JSON.stringify(message, null, 2), 'receive');
                handleMessage(message);
            };

            ws.onclose = function() {
                updateConnectionStatus(false);
                addMessage('WebSocket连接已关闭', 'error');
            };

            ws.onerror = function(error) {
                addMessage('WebSocket错误: ' + error, 'error');
            };
        }

        // 断开连接
        function disconnect() {
            if (ws) {
                ws.close();
            }
        }

        // 处理接收到的消息
        function handleMessage(message) {
            switch (message.type) {
                case 'login_success':
                    updateAuthStatus(true);
                    addMessage('登录成功', 'success');
                    break;
                case 'login_error':
                    addMessage('登录失败: ' + message.data.error, 'error');
                    break;
                case 'logout_success':
                    updateAuthStatus(false);
                    addMessage('登出成功', 'success');
                    break;
                case 'get_latest_tables_start_success':
                    addMessage('查询最新开台数据成功', 'success');
                    displayTablesStartResult(message.data);
                    break;
                case 'get_latest_tables_start_error':
                    addMessage('查询最新开台数据失败: ' + message.data.error, 'error');
                    tablesStartResult.style.display = 'none';
                    break;
            }
        }

        // 显示开台数据结果
        function displayTablesStartResult(data) {
            if (data.tables_start) {
                const ts = data.tables_start;
                const formattedHtml = `
                    <div class="tables-start-info">
                        <h4>开台数据详情</h4>
                        <div class="info-row"><strong>ID:</strong> ${ts.id}</div>
                        <div class="info-row"><strong>桌台ID:</strong> ${ts.table_id}</div>
                        <div class="info-row tables-name"><strong>桌台名称:</strong> ${ts.tables_name || '未知'}</div>
                        <div class="info-row"><strong>账期:</strong> ${ts.account_period}</div>
                        <div class="info-row"><strong>游戏类型:</strong> ${ts.game_type_name} (${ts.game_type})</div>
                        <div class="info-row"><strong>状态:</strong> ${ts.stats_name} (${ts.stats})</div>
                        <div class="info-row"><strong>场次编号:</strong> ${ts.shoe_no}</div>
                        <div class="info-row"><strong>局号编号:</strong> ${ts.hand_no}</div>
                        <div class="info-row"><strong>创建时间:</strong> ${ts.create_time}</div>
                        <div class="info-row"><strong>消息:</strong> ${data.message}</div>
                        ${data.client_ip ? `<div class="info-row"><strong>客户端IP:</strong> ${data.client_ip}</div>` : ''}
                    </div>
                    <div class="raw-json">
                        <h4>原始JSON数据</h4>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </div>
                `;
                tablesStartData.innerHTML = formattedHtml;
            } else {
                tablesStartData.textContent = JSON.stringify(data, null, 2);
            }
            tablesStartResult.style.display = 'block';
        }

        // 发送消息
        function sendMessage(message) {
            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify(message));
                addMessage('发送消息: ' + JSON.stringify(message, null, 2), 'send');
            } else {
                addMessage('WebSocket未连接', 'error');
            }
        }

        // 用户登录
        function login() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            if (!username || !password) {
                addMessage('请输入用户名和密码', 'error');
                return;
            }

            sendMessage({
                type: 'login',
                data: {
                    username: username,
                    password: password
                }
            });
        }

        // 用户登出
        function logout() {
            sendMessage({
                type: 'logout'
            });
        }

        // 查询最新开台数据
        function getLatestTablesStart() {
            sendMessage({
                type: 'get_latest_tables_start'
            });
        }

        // 清空消息
        function clearMessages() {
            messages.innerHTML = '';
        }

        // 页面加载完成后自动连接
        window.addEventListener('load', function() {
            addMessage('页面加载完成，可以点击"连接WebSocket"开始测试', 'success');
        });
    </script>
</body>
</html>