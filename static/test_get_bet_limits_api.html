<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>获取桌台下注限制接口测试工具</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 30px;
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .status-panel {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-connected {
            background-color: #28a745;
        }
        .status-disconnected {
            background-color: #dc3545;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: background-color 0.3s;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-primary:hover {
            background-color: #0056b3;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-success:hover {
            background-color: #1e7e34;
        }
        .btn-info {
            background-color: #17a2b8;
            color: white;
        }
        .btn-info:hover {
            background-color: #138496;
        }
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        .section {
            margin-bottom: 30px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
        }
        .section h2 {
            margin-top: 0;
            color: #495057;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 10px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #495057;
        }
        .form-group select, .form-group input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        .form-inline {
            display: flex;
            gap: 10px;
            align-items: end;
        }
        .form-inline .form-group {
            flex: 1;
            margin-bottom: 0;
        }
        .limits-display {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
        }
        .limit-section {
            background-color: white;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 15px;
        }
        .limit-section h3 {
            margin-top: 0;
            color: #495057;
        }
        .limit-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }
        .limit-item {
            background-color: #f8f9fa;
            padding: 8px;
            border-radius: 4px;
            border-left: 4px solid #007bff;
        }
        .currency-badge {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            margin-right: 5px;
        }
        .currency-chips {
            background-color: #28a745;
            color: white;
        }
        .currency-cash {
            background-color: #ffc107;
            color: #212529;
        }
        .currency-ucode {
            background-color: #17a2b8;
            color: white;
        }
        .messages {
            height: 300px;
            overflow-y: auto;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            background-color: #f8f9fa;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.4;
        }
        .message {
            margin-bottom: 10px;
            padding: 8px;
            border-radius: 4px;
            word-wrap: break-word;
        }
        .message-send {
            background-color: #e3f2fd;
            border-left: 4px solid #2196f3;
        }
        .message-receive {
            background-color: #f3e5f5;
            border-left: 4px solid #9c27b0;
        }
        .message-error {
            background-color: #ffebee;
            border-left: 4px solid #f44336;
        }
        .message-success {
            background-color: #e8f5e8;
            border-left: 4px solid #4caf50;
        }
        .timestamp {
            color: #666;
            font-size: 11px;
            margin-right: 10px;
        }
        .highlight {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>获取桌台下注限制接口测试工具</h1>
        
        <!-- 连接状态面板 -->
        <div class="status-panel">
            <h3>连接状态</h3>
            <div id="connectionStatus">
                <span class="status-indicator status-disconnected"></span>
                <span>未连接</span>
            </div>
            <div style="margin-top: 10px;">
                <button class="btn btn-primary" onclick="connect()">连接WebSocket</button>
                <button class="btn btn-primary" onclick="disconnect()">断开连接</button>
                <button class="btn btn-info" onclick="clearMessages()">清空消息</button>
            </div>
        </div>

        <!-- 用户认证 -->
        <div class="section">
            <h2>用户认证</h2>
            <div class="form-inline">
                <div class="form-group">
                    <label for="username">用户名:</label>
                    <input type="text" id="username" value="admin">
                </div>
                <div class="form-group">
                    <label for="password">密码:</label>
                    <input type="password" id="password" value="123456">
                </div>
                <div class="form-group">
                    <label>&nbsp;</label>
                    <button class="btn btn-success" onclick="login()">登录</button>
                </div>
            </div>
        </div>

        <!-- 获取下注限制 -->
        <div class="section">
            <h2>获取桌台下注限制</h2>
            <div class="highlight">
                <strong>说明：</strong>此接口返回桌台的下注限制信息，包含两个层级：<br>
                1. <strong>桌台级别限制</strong>：当前账期的全局下注限制<br>
                2. <strong>区域级别限制</strong>：每个下注区域的具体限制<br>
                用于客户端录入时的限制提示，不做强制限制。
            </div>
            <div class="form-inline">
                <div class="form-group">
                    <label for="currencyType">货币类型:</label>
                    <select id="currencyType">
                        <option value="1">筹码</option>
                        <option value="2">现金</option>
                        <option value="3">U码</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>&nbsp;</label>
                    <button class="btn btn-info" onclick="getBetLimits()">获取限制信息</button>
                </div>
            </div>
        </div>

        <!-- 限制信息显示 -->
        <div class="section">
            <h2>限制信息显示</h2>
            <div id="limitsDisplay" class="limits-display">
                <p style="text-align: center; color: #6c757d;">点击"获取限制信息"按钮查看结果</p>
            </div>
        </div>

        <!-- 消息日志 -->
        <div class="section">
            <h2>消息日志</h2>
            <div id="messages" class="messages"></div>
        </div>
    </div>

    <script>
        let ws = null;
        let isConnected = false;

        function connect() {
            if (ws) {
                ws.close();
            }
            
            ws = new WebSocket('ws://localhost:8080/ws');
            
            ws.onopen = function() {
                isConnected = true;
                updateConnectionStatus(true);
                addMessage('system', '连接已建立');
            };
            
            ws.onclose = function() {
                isConnected = false;
                updateConnectionStatus(false);
                addMessage('system', '连接已关闭');
            };
            
            ws.onerror = function(error) {
                addMessage('error', '连接错误: ' + error);
            };
            
            ws.onmessage = function(event) {
                const data = JSON.parse(event.data);
                addMessage('receive', JSON.stringify(data, null, 2));
                handleMessage(data);
            };
        }

        function disconnect() {
            if (ws) {
                ws.close();
            }
        }

        function updateConnectionStatus(connected) {
            const statusEl = document.getElementById('connectionStatus');
            if (connected) {
                statusEl.innerHTML = '<span class="status-indicator status-connected"></span><span>已连接</span>';
            } else {
                statusEl.innerHTML = '<span class="status-indicator status-disconnected"></span><span>未连接</span>';
            }
        }

        function sendMessage(message) {
            if (!isConnected) {
                alert('WebSocket未连接');
                return;
            }
            ws.send(JSON.stringify(message));
            addMessage('send', JSON.stringify(message, null, 2));
        }

        function addMessage(type, content) {
            const messagesDiv = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            const timestamp = new Date().toLocaleTimeString();
            
            messageDiv.className = 'message message-' + type;
            messageDiv.innerHTML = '<span class="timestamp">' + timestamp + '</span>' + content;
            
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        function clearMessages() {
            document.getElementById('messages').innerHTML = '';
        }

        function login() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            const message = {
                type: 'login',
                data: {
                    username: username,
                    password: password
                }
            };
            sendMessage(message);
        }

        function getBetLimits() {
            const currencyType = parseInt(document.getElementById('currencyType').value);
            
            const message = {
                type: 'get_bet_limits',
                data: {
                    currency_type: currencyType
                }
            };
            sendMessage(message);
        }

        function handleMessage(data) {
            switch(data.type) {
                case 'get_bet_limits_success':
                    displayLimits(data.data);
                    break;
                case 'get_bet_limits_error':
                    displayError(data.data.error);
                    break;
            }
        }

        function displayLimits(limits) {
            const displayDiv = document.getElementById('limitsDisplay');
            
            let html = '';
            
            // 桌台信息
            html += `<div class="limit-section">`;
            html += `<h3>桌台信息</h3>`;
            html += `<p><strong>桌台ID:</strong> ${limits.table_id}</p>`;
            html += `<p><strong>桌台名称:</strong> ${limits.table_name}</p>`;
            html += `<p><strong>查询货币类型:</strong> ${getCurrencyName(limits.currency_type)}</p>`;
            html += `</div>`;
            
            // 桌台级别限制
            html += `<div class="limit-section">`;
            html += `<h3>桌台级别限制（当前账期全局限制）</h3>`;
            html += `<div class="limit-grid">`;
            html += `<div class="limit-item"><span class="currency-badge currency-chips">筹码</span>最大: ${formatAmount(limits.table_limits.max_bet_chips)}</div>`;
            html += `<div class="limit-item"><span class="currency-badge currency-cash">现金</span>最大: ${formatAmount(limits.table_limits.max_bet_cash)}</div>`;
            html += `<div class="limit-item"><span class="currency-badge currency-ucode">U码</span>最大: ${formatAmount(limits.table_limits.max_bet_u)}</div>`;
            html += `</div>`;
            html += `</div>`;
            
            // 当前货币类型限制提示
            html += `<div class="limit-section">`;
            html += `<h3>当前货币限制提示</h3>`;
            html += `<p><strong>桌台${getCurrencyName(limits.currency_type)}最大限制:</strong> ${formatAmount(limits.current_limits.table_max_amount)}</p>`;
            html += `<div class="limit-grid">`;
            limits.current_limits.area_limits.forEach(area => {
                html += `<div class="limit-item">${area.bet_area}: ${formatAmount(area.max_amount)}</div>`;
            });
            html += `</div>`;
            html += `</div>`;
            
            // 所有下注区域详细限制
            html += `<div class="limit-section">`;
            html += `<h3>下注区域详细限制</h3>`;
            limits.area_limits.forEach(area => {
                html += `<div style="margin-bottom: 10px; padding: 10px; background-color: #f8f9fa; border-radius: 4px;">`;
                html += `<h4 style="margin: 0 0 8px 0;">${area.bet_area} (赔率: ${area.odds})</h4>`;
                html += `<div class="limit-grid">`;
                html += `<div class="limit-item"><span class="currency-badge currency-chips">筹码</span>最大: ${formatAmount(area.max_bet_chips)}</div>`;
                html += `<div class="limit-item"><span class="currency-badge currency-cash">现金</span>最大: ${formatAmount(area.max_bet_cash)}</div>`;
                html += `<div class="limit-item"><span class="currency-badge currency-ucode">U码</span>最大: ${formatAmount(area.max_bet_u)}</div>`;
                html += `</div>`;
                html += `</div>`;
            });
            html += `</div>`;
            
            displayDiv.innerHTML = html;
        }

        function displayError(error) {
            const displayDiv = document.getElementById('limitsDisplay');
            displayDiv.innerHTML = `<div style="color: red; text-align: center; padding: 20px;">错误: ${error}</div>`;
        }

        function getCurrencyName(type) {
            switch(type) {
                case 1: return '筹码';
                case 2: return '现金';
                case 3: return 'U码';
                default: return '未知';
            }
        }

        function formatAmount(amount) {
            if (amount === 0) return '0';
            return new Intl.NumberFormat('zh-CN').format(amount);
        }

        // 页面加载完成后自动连接
        window.onload = function() {
            connect();
        };
    </script>
</body>
</html> 