<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试获取条口记录结果接口（露珠图数据）</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .status.connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .result-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        .result-table th,
        .result-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .result-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .result-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .highlight {
            background-color: #fff3cd;
            padding: 15px;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .error {
            color: #dc3545;
            font-weight: bold;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>测试获取条口记录结果接口（露珠图数据）</h1>
        
        <div class="highlight">
            <strong>接口说明：</strong> 查询hand_records中当前桌台当前账期所有场次局数的结果result_1，用于前端生成露珠图。系统会根据客户端IP自动获取对应的桌台信息。
        </div>
        
        <div class="section">
            <h3>连接状态</h3>
            <div id="connectionStatus" class="status disconnected">未连接</div>
            <button onclick="connect()">连接WebSocket</button>
            <button onclick="disconnect()">断开连接</button>
        </div>

        <div class="section">
            <h3>用户登录</h3>
            <div class="form-group">
                <label for="username">用户名:</label>
                <input type="text" id="username" value="admin">
            </div>
            <div class="form-group">
                <label for="password">密码:</label>
                <input type="password" id="password" value="123456">
            </div>
            <button onclick="login()">登录</button>
        </div>

        <div class="section">
            <h3>获取条口记录结果（露珠图数据）</h3>
            <div class="form-group">
                <label for="accountPeriod">账期 (YYYYMMDD格式):</label>
                <input type="text" id="accountPeriod" value="********" placeholder="例如: ********">
            </div>
            <button onclick="getHandRecordsResult()">查询条口记录结果</button>
            <button onclick="getHandRecordsResultToday()">查询今日条口记录</button>
        </div>

        <div class="section">
            <h3>查询结果</h3>
            <div id="resultSummary"></div>
            <div id="resultTable"></div>
        </div>

        <div class="section">
            <h3>消息日志</h3>
            <button onclick="clearLog()">清空日志</button>
            <div id="messageLog" class="log"></div>
        </div>
    </div>

    <script>
        let ws = null;
        let isConnected = false;

        function updateConnectionStatus(connected) {
            isConnected = connected;
            const statusElement = document.getElementById('connectionStatus');
            if (connected) {
                statusElement.textContent = '已连接';
                statusElement.className = 'status connected';
            } else {
                statusElement.textContent = '未连接';
                statusElement.className = 'status disconnected';
            }
        }

        function logMessage(message, type = 'info') {
            const log = document.getElementById('messageLog');
            const timestamp = new Date().toLocaleTimeString();
            const messageElement = document.createElement('div');
            
            let className = '';
            if (type === 'error') className = 'error';
            else if (type === 'success') className = 'success';
            
            messageElement.innerHTML = `<span class="${className}">[${timestamp}] ${message}</span>`;
            log.appendChild(messageElement);
            log.scrollTop = log.scrollHeight;
        }

        function connect() {
            if (ws && isConnected) {
                logMessage('WebSocket已连接', 'info');
                return;
            }

            ws = new WebSocket('ws://localhost:8080/ws');
            
            ws.onopen = function() {
                updateConnectionStatus(true);
                logMessage('WebSocket连接成功', 'success');
            };

            ws.onmessage = function(event) {
                const message = JSON.parse(event.data);
                logMessage('收到消息: ' + JSON.stringify(message, null, 2), 'info');
                
                // 处理不同类型的消息
                switch(message.type) {
                    case 'login_success':
                        logMessage('登录成功', 'success');
                        break;
                    case 'login_error':
                        logMessage('登录失败: ' + message.data.error, 'error');
                        break;
                    case 'get_hand_records_result_success':
                        logMessage('条口记录查询成功', 'success');
                        displayResults(message.data);
                        break;
                    case 'get_hand_records_result_error':
                        logMessage('条口记录查询失败: ' + message.data.error, 'error');
                        clearResults();
                        break;
                }
            };

            ws.onclose = function() {
                updateConnectionStatus(false);
                logMessage('WebSocket连接已关闭', 'info');
            };

            ws.onerror = function(error) {
                logMessage('WebSocket错误: ' + error, 'error');
                updateConnectionStatus(false);
            };
        }

        function disconnect() {
            if (ws) {
                ws.close();
                ws = null;
                updateConnectionStatus(false);
                logMessage('WebSocket连接已断开', 'info');
            }
        }

        function login() {
            if (!isConnected) {
                logMessage('请先连接WebSocket', 'error');
                return;
            }

            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            if (!username || !password) {
                logMessage('用户名和密码不能为空', 'error');
                return;
            }

            const loginMessage = {
                type: 'login',
                data: {
                    username: username,
                    password: password
                }
            };

            ws.send(JSON.stringify(loginMessage));
            logMessage('发送登录请求: ' + JSON.stringify(loginMessage, null, 2), 'info');
        }

        function getHandRecordsResult() {
            if (!isConnected) {
                logMessage('请先连接WebSocket', 'error');
                return;
            }

            const accountPeriod = document.getElementById('accountPeriod').value;

            if (!accountPeriod) {
                logMessage('账期不能为空', 'error');
                return;
            }

            // 验证账期格式 (YYYYMMDD)
            if (!/^\d{8}$/.test(accountPeriod)) {
                logMessage('账期格式错误，请使用YYYYMMDD格式，例如: ********', 'error');
                return;
            }

            const queryMessage = {
                type: 'get_hand_records_result',
                data: {
                    account_period: accountPeriod
                }
            };

            ws.send(JSON.stringify(queryMessage));
            logMessage('发送条口记录查询请求: ' + JSON.stringify(queryMessage, null, 2), 'info');
        }

        function getHandRecordsResultToday() {
            // 自动设置为今天的日期
            const today = new Date();
            const year = today.getFullYear();
            const month = String(today.getMonth() + 1).padStart(2, '0');
            const day = String(today.getDate()).padStart(2, '0');
            const todayStr = `${year}${month}${day}`;
            
            document.getElementById('accountPeriod').value = todayStr;
            logMessage('设置账期为今日: ' + todayStr, 'info');
            
            getHandRecordsResult();
        }

        function displayResults(data) {
            const summaryDiv = document.getElementById('resultSummary');
            const tableDiv = document.getElementById('resultTable');

            // 显示汇总信息
            summaryDiv.innerHTML = `
                <div style="background-color: #e8f5e8; padding: 15px; border-radius: 5px; margin-bottom: 15px;">
                    <h4 style="margin: 0 0 10px 0;">查询结果汇总</h4>
                    <p><strong>桌台ID:</strong> ${data.table_id}</p>
                    <p><strong>桌台编号:</strong> ${data.table_code}</p>
                    <p><strong>总记录数:</strong> ${data.total_count} 条</p>
                    <p><strong>客户端IP:</strong> ${data.client_ip}</p>
                    <p><strong>消息:</strong> ${data.message}</p>
                </div>
            `;

            if (data.records && data.records.length > 0) {
                // 显示详细数据表格
                let tableHTML = `
                    <h4>条口记录详情（按场次和局数排序）</h4>
                    <table class="result-table">
                        <thead>
                            <tr>
                                <th>记录ID</th>
                                <th>桌台ID</th>
                                <th>账期</th>
                                <th>场次编号</th>
                                <th>局号编号</th>
                                <th>游戏结果</th>
                                <th>创建时间</th>
                            </tr>
                        </thead>
                        <tbody>
                `;

                data.records.forEach(record => {
                    tableHTML += `
                        <tr>
                            <td>${record.id}</td>
                            <td>${record.table_id}</td>
                            <td>${record.account_period}</td>
                            <td>${record.shoe_no}</td>
                            <td>${record.hand_no}</td>
                            <td><strong>${record.result_1}</strong></td>
                            <td>${record.create_time}</td>
                        </tr>
                    `;
                });

                tableHTML += `
                        </tbody>
                    </table>
                `;

                tableDiv.innerHTML = tableHTML;

                // 生成简单的露珠图预览
                generatePearlRoadPreview(data.records);
            } else {
                tableDiv.innerHTML = '<p style="color: #666; font-style: italic;">没有找到条口记录数据</p>';
            }
        }

        function generatePearlRoadPreview(records) {
            const tableDiv = document.getElementById('resultTable');
            
            if (records.length === 0) return;

            let pearlRoadHTML = `
                <div style="margin-top: 30px;">
                    <h4>露珠图预览（仅显示游戏结果）</h4>
                    <div style="display: flex; flex-wrap: wrap; gap: 5px; padding: 15px; background-color: #f8f9fa; border-radius: 5px;">
            `;

            records.forEach((record, index) => {
                let resultColor = '#666';
                let resultText = record.result_1;
                
                // 根据不同结果设置颜色
                switch(record.result_1) {
                    case '庄':
                        resultColor = '#dc3545'; // 红色
                        break;
                    case '闲':
                        resultColor = '#007bff'; // 蓝色
                        break;
                    case '和':
                        resultColor = '#28a745'; // 绿色
                        break;
                    default:
                        resultColor = '#666'; // 灰色
                }

                pearlRoadHTML += `
                    <div style="
                        width: 30px; 
                        height: 30px; 
                        background-color: ${resultColor}; 
                        color: white; 
                        display: flex; 
                        align-items: center; 
                        justify-content: center; 
                        border-radius: 3px; 
                        font-size: 12px; 
                        font-weight: bold;
                        border: 1px solid #ccc;
                    " title="场次${record.shoe_no}-局${record.hand_no}: ${resultText}">
                        ${resultText}
                    </div>
                `;
            });

            pearlRoadHTML += `
                    </div>
                    <p style="font-size: 12px; color: #666; margin-top: 10px;">
                        * 这是一个简单的露珠图预览，实际应用中需要根据具体的露珠图规则进行排列和显示
                    </p>
                </div>
            `;

            tableDiv.innerHTML += pearlRoadHTML;
        }

        function clearResults() {
            document.getElementById('resultSummary').innerHTML = '';
            document.getElementById('resultTable').innerHTML = '';
        }

        function clearLog() {
            document.getElementById('messageLog').innerHTML = '';
        }

        // 页面加载时自动连接
        window.onload = function() {
            logMessage('页面加载完成，准备连接WebSocket', 'info');
        };
    </script>
</body>
</html> 