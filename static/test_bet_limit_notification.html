<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>下注超限主动推送通知测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status-container {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        .status-box {
            padding: 10px;
            border-radius: 5px;
            font-weight: bold;
            min-width: 120px;
            text-align: center;
        }
        .status-connected { background-color: #d4edda; color: #155724; }
        .status-disconnected { background-color: #f8d7da; color: #721c24; }
        .status-authenticated { background-color: #cce7ff; color: #004085; }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button.danger {
            background-color: #dc3545;
        }
        button.danger:hover {
            background-color: #c82333;
        }
        button.warning {
            background-color: #ffc107;
            color: #212529;
        }
        button.warning:hover {
            background-color: #e0a800;
        }
        .bet-area-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .bet-area-card {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            background-color: #f9f9f9;
        }
        .bet-area-card.exceeded {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        .limit-info {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        .notification-area {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
            display: none;
        }
        .notification-area.show {
            display: block;
        }
        .notification-area.error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        .notification-area.success {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .log-area {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 14px;
        }
        .log-entry {
            margin-bottom: 5px;
            padding: 5px;
            border-radius: 3px;
        }
        .log-send { background-color: #e1f5fe; }
        .log-receive { background-color: #f3e5f5; }
        .log-error { background-color: #ffebee; }
        .log-notification { background-color: #fff3e0; }
        .current-limits {
            background-color: #e8f4f8;
            border: 1px solid #bee5eb;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .tab-container {
            border-bottom: 1px solid #ddd;
            margin-bottom: 20px;
        }
        .tab-button {
            background: none;
            border: none;
            padding: 10px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
        }
        .tab-button.active {
            border-bottom-color: #007bff;
            background-color: #f8f9fa;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚨 下注超限主动推送通知测试</h1>
        
        <!-- 连接状态 -->
        <div class="status-container">
            <div id="connectionStatus" class="status-box status-disconnected">未连接</div>
            <div id="authStatus" class="status-box status-disconnected">未登录</div>
            <div id="tableStatus" class="status-box status-disconnected">桌台未知</div>
        </div>

        <!-- 通知区域 -->
        <div id="notificationArea" class="notification-area">
            <strong>通知:</strong> <span id="notificationText"></span>
        </div>

        <!-- 选项卡 -->
        <div class="tab-container">
            <button class="tab-button active" onclick="switchTab('connection')">连接管理</button>
            <button class="tab-button" onclick="switchTab('limits')">限制查询</button>
            <button class="tab-button" onclick="switchTab('betting')">下注测试</button>
            <button class="tab-button" onclick="switchTab('logs')">消息日志</button>
        </div>

        <!-- 连接管理选项卡 -->
        <div id="connectionTab" class="tab-content active">
            <h3>连接管理</h3>
            <div class="form-group">
                <button onclick="connectWebSocket()">连接WebSocket</button>
                <button onclick="disconnectWebSocket()">断开连接</button>
            </div>
            
            <h4>用户登录</h4>
            <div class="form-group">
                <label>用户名:</label>
                <input type="text" id="username" value="admin">
            </div>
            <div class="form-group">
                <label>密码:</label>
                <input type="password" id="password" value="123456">
            </div>
            <div class="form-group">
                <button onclick="login()">登录</button>
                <button onclick="logout()">登出</button>
            </div>
        </div>

        <!-- 限制查询选项卡 -->
        <div id="limitsTab" class="tab-content">
            <h3>下注限制查询</h3>
            <div class="form-group">
                <label>货币类型:</label>
                <select id="currencyType">
                    <option value="1">筹码</option>
                    <option value="2">现金</option>
                    <option value="3">U码</option>
                </select>
            </div>
            <div class="form-group">
                <button onclick="getBetLimits()">查询下注限制</button>
                <button onclick="getTableInfo()">获取桌台信息</button>
            </div>

            <!-- 当前限制信息 -->
            <div id="currentLimits" class="current-limits" style="display: none;">
                <h4>当前桌台限制信息</h4>
                <div id="limitsDisplay"></div>
            </div>
        </div>

        <!-- 下注测试选项卡 -->
        <div id="bettingTab" class="tab-content">
            <h3>下注超限测试</h3>
            <p>以下测试会触发不同的超限情况，服务端会主动推送超限通知：</p>
            
            <div class="form-group">
                <label>测试账期:</label>
                <input type="text" id="accountPeriod" value="********">
            </div>
            <div class="form-group">
                <label>场次编号:</label>
                <input type="number" id="roundNo" value="1">
            </div>
            <div class="form-group">
                <label>局号编号:</label>
                <input type="number" id="handNo" value="1">
            </div>
            <div class="form-group">
                <label>洗码号:</label>
                <input type="text" id="washCode" value="TEST001">
            </div>
            <div class="form-group">
                <label>客户姓名:</label>
                <input type="text" id="userName" value="测试客户">
            </div>

            <h4>下注区域 (输入超限金额测试主动推送)</h4>
            <div class="bet-area-grid">
                <div class="bet-area-card">
                    <label>庄:</label>
                    <input type="number" id="bankerAmount" value="0" step="0.01">
                    <div class="limit-info">区域限制: 等待查询...</div>
                </div>
                <div class="bet-area-card">
                    <label>闲:</label>
                    <input type="number" id="playerAmount" value="0" step="0.01">
                    <div class="limit-info">区域限制: 等待查询...</div>
                </div>
                <div class="bet-area-card">
                    <label>和:</label>
                    <input type="number" id="tieAmount" value="0" step="0.01">
                    <div class="limit-info">区域限制: 等待查询...</div>
                </div>
                <div class="bet-area-card">
                    <label>庄对:</label>
                    <input type="number" id="bankerPairAmount" value="0" step="0.01">
                    <div class="limit-info">区域限制: 等待查询...</div>
                </div>
                <div class="bet-area-card">
                    <label>闲对:</label>
                    <input type="number" id="playerPairAmount" value="0" step="0.01">
                    <div class="limit-info">区域限制: 等待查询...</div>
                </div>
                <div class="bet-area-card">
                    <label>幸运6:</label>
                    <input type="number" id="lucky6Amount" value="0" step="0.01">
                    <div class="limit-info">区域限制: 等待查询...</div>
                </div>
                <div class="bet-area-card">
                    <label>幸运7:</label>
                    <input type="number" id="lucky7Amount" value="0" step="0.01">
                    <div class="limit-info">区域限制: 等待查询...</div>
                </div>
            </div>

            <div class="form-group">
                <button onclick="submitBet()" class="danger">提交下注 (测试超限通知)</button>
                <button onclick="setExceedingBets()" class="warning">设置超限金额</button>
                <button onclick="setNormalBets()">设置正常金额</button>
                <button onclick="clearBets()">清空所有下注</button>
            </div>
        </div>

        <!-- 消息日志选项卡 -->
        <div id="logsTab" class="tab-content">
            <h3>WebSocket 消息日志</h3>
            <div class="form-group">
                <button onclick="clearLogs()">清空日志</button>
                <button onclick="exportLogs()">导出日志</button>
            </div>
            <div id="logArea" class="log-area"></div>
        </div>
    </div>

    <script>
        let ws = null;
        let isAuthenticated = false;
        let currentTableId = null;
        let currentLimitsData = null;

        // 切换选项卡
        function switchTab(tabName) {
            // 隐藏所有选项卡内容
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 移除所有按钮的active状态
            document.querySelectorAll('.tab-button').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // 显示指定选项卡
            document.getElementById(tabName + 'Tab').classList.add('active');
            event.target.classList.add('active');
        }

        // WebSocket连接管理
        function connectWebSocket() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                showNotification('WebSocket已经连接', 'warning');
                return;
            }

            ws = new WebSocket('ws://localhost:8080/ws');
            
            ws.onopen = function(event) {
                showNotification('WebSocket连接成功', 'success');
                document.getElementById('connectionStatus').textContent = '已连接';
                document.getElementById('connectionStatus').className = 'status-box status-connected';
                addLog('connection', 'WebSocket连接成功');
            };
            
            ws.onmessage = function(event) {
                const message = JSON.parse(event.data);
                addLog('receive', `收到: ${message.type}`, message);
                handleMessage(message);
            };
            
            ws.onclose = function(event) {
                showNotification('WebSocket连接关闭', 'error');
                document.getElementById('connectionStatus').textContent = '未连接';
                document.getElementById('connectionStatus').className = 'status-box status-disconnected';
                addLog('connection', 'WebSocket连接关闭');
            };
            
            ws.onerror = function(error) {
                showNotification('WebSocket连接错误', 'error');
                addLog('error', 'WebSocket连接错误', error);
            };
        }

        function disconnectWebSocket() {
            if (ws) {
                ws.close();
                ws = null;
            }
        }

        // 用户认证
        function login() {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                showNotification('请先连接WebSocket', 'error');
                return;
            }

            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            const message = {
                type: 'login',
                data: {
                    username: username,
                    password: password
                }
            };

            ws.send(JSON.stringify(message));
            addLog('send', '发送登录请求', message);
        }

        function logout() {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                showNotification('WebSocket未连接', 'error');
                return;
            }

            const message = { type: 'logout' };
            ws.send(JSON.stringify(message));
            addLog('send', '发送登出请求', message);
        }

        // 获取桌台信息
        function getTableInfo() {
            if (!checkAuthentication()) return;

            const message = { type: 'get_table_info' };
            ws.send(JSON.stringify(message));
            addLog('send', '获取桌台信息', message);
        }

        // 获取下注限制
        function getBetLimits() {
            if (!checkAuthentication()) return;

            const currencyType = parseInt(document.getElementById('currencyType').value);
            const message = {
                type: 'get_bet_limits',
                data: {
                    currency_type: currencyType
                }
            };

            ws.send(JSON.stringify(message));
            addLog('send', '获取下注限制', message);
        }

        // 提交下注
        function submitBet() {
            if (!checkAuthentication()) return;

            const betData = {
                type: 'table_batch_bet_entry',
                data: {
                    bet_record: {
                        game_type: 1,
                        account_period: document.getElementById('accountPeriod').value,
                        round_no: parseInt(document.getElementById('roundNo').value),
                        hand_no: parseInt(document.getElementById('handNo').value),
                        wash_code: document.getElementById('washCode').value,
                        user_name: document.getElementById('userName').value,
                        currency_type: parseInt(document.getElementById('currencyType').value),
                        banker_amount: parseFloat(document.getElementById('bankerAmount').value) || 0,
                        player_amount: parseFloat(document.getElementById('playerAmount').value) || 0,
                        tie_amount: parseFloat(document.getElementById('tieAmount').value) || 0,
                        banker_pair_amount: parseFloat(document.getElementById('bankerPairAmount').value) || 0,
                        player_pair_amount: parseFloat(document.getElementById('playerPairAmount').value) || 0,
                        lucky_6_amount: parseFloat(document.getElementById('lucky6Amount').value) || 0,
                        lucky_7_amount: parseFloat(document.getElementById('lucky7Amount').value) || 0,
                        win_result: '',
                        win_loss: 0,
                        loss: 0,
                        amount_tip: 0,
                        amount_bottom: 0,
                        wash_rate: 0.0018,
                        wash_amount: 0,
                        wash_tip: 0
                    }
                }
            };

            ws.send(JSON.stringify(betData));
            addLog('send', '提交下注 (可能触发超限通知)', betData);
        }

        // 设置超限金额
        function setExceedingBets() {
            if (!currentLimitsData) {
                showNotification('请先查询下注限制', 'warning');
                return;
            }

            // 设置一些明显超限的金额
            document.getElementById('bankerAmount').value = '999999.00';  // 超过大部分限制
            document.getElementById('playerAmount').value = '888888.00';
            document.getElementById('tieAmount').value = '777777.00';
            document.getElementById('bankerPairAmount').value = '666666.00';
            
            showNotification('已设置超限金额，点击提交下注将触发超限通知', 'warning');
            highlightExceedingAreas();
        }

        // 设置正常金额
        function setNormalBets() {
            document.getElementById('bankerAmount').value = '1000.00';
            document.getElementById('playerAmount').value = '500.00';
            document.getElementById('tieAmount').value = '100.00';
            document.getElementById('bankerPairAmount').value = '50.00';
            document.getElementById('playerPairAmount').value = '50.00';
            document.getElementById('lucky6Amount').value = '25.00';
            document.getElementById('lucky7Amount').value = '25.00';
            
            showNotification('已设置正常金额', 'success');
            clearExceedingHighlight();
        }

        // 清空所有下注
        function clearBets() {
            document.getElementById('bankerAmount').value = '0';
            document.getElementById('playerAmount').value = '0';
            document.getElementById('tieAmount').value = '0';
            document.getElementById('bankerPairAmount').value = '0';
            document.getElementById('playerPairAmount').value = '0';
            document.getElementById('lucky6Amount').value = '0';
            document.getElementById('lucky7Amount').value = '0';
            
            clearExceedingHighlight();
        }

        // 高亮超限区域
        function highlightExceedingAreas() {
            document.querySelectorAll('.bet-area-card').forEach(card => {
                card.classList.add('exceeded');
            });
        }

        // 清除超限高亮
        function clearExceedingHighlight() {
            document.querySelectorAll('.bet-area-card').forEach(card => {
                card.classList.remove('exceeded');
            });
        }

        // 处理WebSocket消息
        function handleMessage(message) {
            switch (message.type) {
                case 'connection':
                    break;

                case 'login_success':
                    isAuthenticated = true;
                    document.getElementById('authStatus').textContent = `已登录 (${message.data.user_info.realname})`;
                    document.getElementById('authStatus').className = 'status-box status-authenticated';
                    showNotification('登录成功', 'success');
                    
                    // 登录成功后自动获取桌台信息
                    setTimeout(() => getTableInfo(), 1000);
                    break;

                case 'login_error':
                    showNotification(`登录失败: ${message.data.error}`, 'error');
                    break;

                case 'logout_success':
                    isAuthenticated = false;
                    document.getElementById('authStatus').textContent = '未登录';
                    document.getElementById('authStatus').className = 'status-box status-disconnected';
                    showNotification('登出成功', 'success');
                    break;

                case 'table_info_success':
                    currentTableId = message.data.table.id;
                    document.getElementById('tableStatus').textContent = `桌台: ${message.data.table.table_name}`;
                    document.getElementById('tableStatus').className = 'status-box status-connected';
                    showNotification(`获取桌台信息成功: ${message.data.table.table_name}`, 'success');
                    
                    // 自动获取下注限制
                    setTimeout(() => getBetLimits(), 500);
                    break;

                case 'table_info_error':
                    showNotification(`获取桌台信息失败: ${message.data.error}`, 'error');
                    break;

                case 'get_bet_limits_success':
                    currentLimitsData = message.data;
                    displayBetLimits(message.data);
                    showNotification('获取下注限制成功', 'success');
                    break;

                case 'get_bet_limits_error':
                    showNotification(`获取下注限制失败: ${message.data.error}`, 'error');
                    break;

                case 'single_bet_entry_success':
                    showNotification('下注提交成功', 'success');
                    break;

                case 'single_bet_entry_error':
                    showNotification(`下注失败: ${message.data.error}`, 'error');
                    break;

                case 'bet_limit_exceeded':
                    // 🚨 主要功能：处理超限通知
                    handleBetLimitExceededNotification(message.data);
                    break;

                default:
                    addLog('receive', `未处理的消息类型: ${message.type}`, message);
            }
        }

        // 🚨 处理下注超限通知 (核心功能)
        function handleBetLimitExceededNotification(data) {
            addLog('notification', '🚨 收到下注超限通知', data);
            
            // 显示超限警告
            showBetLimitExceededAlert(data);
            
            // 高亮违规区域
            highlightViolatedAreas(data.violated_areas);
            
            // 播放提示音
            playWarningSound();
            
            // 更新通知区域
            showNotification(`🚨 下注超限！违规区域: ${data.violation_count} 个`, 'error');
        }

        // 显示超限详细警告
        function showBetLimitExceededAlert(data) {
            let alertText = `🚨 下注金额超出限制!\n\n`;
            alertText += `桌台: ${data.table_info.table_name}\n`;
            alertText += `货币类型: ${data.table_info.currency_name}\n`;
            alertText += `违规区域数量: ${data.violation_count}\n\n`;
            alertText += `详细违规信息:\n`;
            
            data.violated_areas.forEach((area, index) => {
                alertText += `${index + 1}. ${area.bet_area}:\n`;
                alertText += `   下注金额: ${area.bet_amount.toLocaleString()}\n`;
                alertText += `   最大限制: ${area.max_allowed.toLocaleString()}\n`;
                alertText += `   超出金额: ${area.exceeded_by.toLocaleString()}\n`;
                alertText += `   限制类型: ${area.limit_type}\n\n`;
            });
            
            alert(alertText);
        }

        // 高亮违规区域
        function highlightViolatedAreas(violatedAreas) {
            // 清除之前的高亮
            clearExceedingHighlight();
            
            // 高亮违规区域
            violatedAreas.forEach(area => {
                const areaMap = {
                    '庄': 'bankerAmount',
                    '闲': 'playerAmount',
                    '和': 'tieAmount',
                    '庄对': 'bankerPairAmount',
                    '闲对': 'playerPairAmount',
                    '幸运6': 'lucky6Amount',
                    '幸运7': 'lucky7Amount'
                };
                
                const inputId = areaMap[area.bet_area];
                if (inputId) {
                    const input = document.getElementById(inputId);
                    if (input) {
                        input.parentElement.classList.add('exceeded');
                    }
                }
            });
        }

        // 播放警告音
        function playWarningSound() {
            // 创建音频上下文播放提示音
            try {
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();
                
                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);
                
                oscillator.frequency.value = 800;
                oscillator.type = 'sine';
                
                gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 1);
                
                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + 1);
            } catch (e) {
                console.log('无法播放提示音:', e);
            }
        }

        // 显示下注限制信息
        function displayBetLimits(limitsData) {
            const container = document.getElementById('currentLimits');
            const display = document.getElementById('limitsDisplay');
            
            let html = `
                <div><strong>桌台:</strong> ${limitsData.table_name}</div>
                <div><strong>货币类型:</strong> ${getCurrentCurrencyName(limitsData.currency_type)}</div>
                <div><strong>桌台最大限制:</strong> ${limitsData.current_limits.table_max_amount.toLocaleString()}</div>
                <h5>各区域限制:</h5>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 10px;">
            `;
            
            limitsData.current_limits.area_limits.forEach(area => {
                html += `
                    <div style="background: #f8f9fa; padding: 8px; border-radius: 3px;">
                        <strong>${area.bet_area}:</strong><br>
                        ${area.max_amount.toLocaleString()}
                    </div>
                `;
            });
            
            html += '</div>';
            display.innerHTML = html;
            container.style.display = 'block';

            // 更新下注区域的限制提示
            updateBetAreaLimits(limitsData);
        }

        // 更新下注区域限制提示
        function updateBetAreaLimits(limitsData) {
            const areaMap = {
                '庄': 'bankerAmount',
                '闲': 'playerAmount',
                '和': 'tieAmount',
                '庄对': 'bankerPairAmount',
                '闲对': 'playerPairAmount',
                '幸运6': 'lucky6Amount',
                '幸运7': 'lucky7Amount'
            };

            limitsData.current_limits.area_limits.forEach(area => {
                const inputId = areaMap[area.bet_area];
                if (inputId) {
                    const input = document.getElementById(inputId);
                    if (input) {
                        const limitInfo = input.parentElement.querySelector('.limit-info');
                        if (limitInfo) {
                            limitInfo.textContent = `区域限制: ${area.max_amount.toLocaleString()}`;
                        }
                    }
                }
            });
        }

        // 辅助函数
        function getCurrentCurrencyName(currencyType) {
            const names = { 1: '筹码', 2: '现金', 3: 'U码' };
            return names[currencyType] || '未知';
        }

        function checkAuthentication() {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                showNotification('WebSocket未连接', 'error');
                return false;
            }
            if (!isAuthenticated) {
                showNotification('请先登录', 'error');
                return false;
            }
            return true;
        }

        function showNotification(text, type = 'info') {
            const area = document.getElementById('notificationArea');
            const textElement = document.getElementById('notificationText');
            
            textElement.textContent = text;
            area.className = `notification-area show ${type}`;
            
            setTimeout(() => {
                area.classList.remove('show');
            }, 5000);
        }

        function addLog(type, message, data = null) {
            const logArea = document.getElementById('logArea');
            const timestamp = new Date().toLocaleTimeString();
            
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            
            let logText = `[${timestamp}] ${message}`;
            if (data) {
                logText += `\n${JSON.stringify(data, null, 2)}`;
            }
            
            logEntry.textContent = logText;
            logArea.appendChild(logEntry);
            logArea.scrollTop = logArea.scrollHeight;
        }

        function clearLogs() {
            document.getElementById('logArea').innerHTML = '';
        }

        function exportLogs() {
            const logs = document.getElementById('logArea').textContent;
            const blob = new Blob([logs], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `bet_limit_test_logs_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            
            URL.revokeObjectURL(url);
        }

        // 页面加载完成后自动连接
        window.onload = function() {
            connectWebSocket();
        };

        // 页面关闭时断开连接
        window.onbeforeunload = function() {
            if (ws) {
                ws.close();
            }
        };
    </script>
</body>
</html> 