<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>下注相关接口测试工具</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 30px;
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .status-panel {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-connected {
            background-color: #28a745;
        }
        .status-disconnected {
            background-color: #dc3545;
        }
        .control-panel {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-primary:hover {
            background-color: #0056b3;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-success:hover {
            background-color: #1e7e34;
        }
        .btn-warning {
            background-color: #ffc107;
            color: #212529;
        }
        .btn-warning:hover {
            background-color: #e0a800;
        }
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        .btn-danger:hover {
            background-color: #c82333;
        }
        .btn-info {
            background-color: #17a2b8;
            color: white;
        }
        .btn-info:hover {
            background-color: #138496;
        }
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        .section {
            margin-bottom: 30px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
        }
        .section h2 {
            margin-top: 0;
            color: #495057;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 10px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #495057;
        }
        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        .form-group input:focus, .form-group select:focus, .form-group textarea:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
        }
        .form-inline {
            display: flex;
            gap: 10px;
            align-items: end;
        }
        .form-inline .form-group {
            flex: 1;
            margin-bottom: 0;
        }
        .messages {
            height: 400px;
            overflow-y: auto;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            background-color: #f8f9fa;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.4;
        }
        .message {
            margin-bottom: 10px;
            padding: 8px;
            border-radius: 4px;
            word-wrap: break-word;
        }
        .message-send {
            background-color: #e3f2fd;
            border-left: 4px solid #2196f3;
        }
        .message-receive {
            background-color: #f3e5f5;
            border-left: 4px solid #9c27b0;
        }
        .message-error {
            background-color: #ffebee;
            border-left: 4px solid #f44336;
        }
        .message-success {
            background-color: #e8f5e8;
            border-left: 4px solid #4caf50;
        }
        .timestamp {
            color: #666;
            font-size: 11px;
            margin-right: 10px;
        }
        .bet-record {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 10px;
        }
        .bet-record h4 {
            margin-top: 0;
            color: #495057;
        }
        .bet-fields {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
        }
        .add-bet-btn {
            background-color: #28a745;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
        }
        .remove-bet-btn {
            background-color: #dc3545;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
        }
        .result-options {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 10px;
        }
        .result-option {
            background-color: #e9ecef;
            border: 1px solid #ced4da;
            border-radius: 4px;
            padding: 5px 10px;
            cursor: pointer;
            user-select: none;
        }
        .result-option.selected {
            background-color: #007bff;
            color: white;
            border-color: #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>下注相关接口测试工具</h1>
        
        <!-- 连接状态面板 -->
        <div class="status-panel">
            <div>
                <span class="status-indicator" id="statusIndicator"></span>
                <span id="statusText">未连接</span>
            </div>
            <div style="margin-top: 10px;">
                <button class="btn btn-primary" id="connectBtn" onclick="connect()">连接</button>
                <button class="btn btn-danger" id="disconnectBtn" onclick="disconnect()" disabled>断开</button>
            </div>
        </div>

        <!-- 控制面板 -->
        <div class="control-panel">
            <button class="btn btn-success" onclick="login()">登录</button>
            <button class="btn btn-info" onclick="getTableInfo()">获取桌台信息</button>
            <button class="btn btn-warning" onclick="clearMessages()">清空消息</button>
        </div>



        <!-- 百家乐结果录入测试 -->
        <div class="section">
            <h2>百家乐结果录入测试</h2>
            <div class="form-inline">
                <div class="form-group">
                    <label>桌台ID:</label>
                    <input type="number" id="resultTableId" value="1" min="1">
                </div>
                            <div class="form-group">
                <label>账期:</label>
                <input type="text" id="resultAccountPeriod" value="********" placeholder="YYYYMMDD">
            </div>
                <div class="form-group">
                    <label>场次编号:</label>
                    <input type="number" id="resultRoundNo" value="1" min="1">
                </div>
                <div class="form-group">
                    <label>局号编号:</label>
                    <input type="number" id="resultHandNo" value="1" min="1">
                </div>
            </div>
            
            <div class="form-group">
                <label>游戏结果:</label>
                <div class="result-options">
                    <div class="result-option" onclick="toggleResult(this)" data-result="庄">庄</div>
                    <div class="result-option" onclick="toggleResult(this)" data-result="闲">闲</div>
                    <div class="result-option" onclick="toggleResult(this)" data-result="和">和</div>
                    <div class="result-option" onclick="toggleResult(this)" data-result="庄对">庄对</div>
                    <div class="result-option" onclick="toggleResult(this)" data-result="闲对">闲对</div>
                    <div class="result-option" onclick="toggleResult(this)" data-result="幸运6">幸运6</div>
                    <div class="result-option" onclick="toggleResult(this)" data-result="幸运7">幸运7</div>
                </div>
                <div style="margin-top: 10px;">
                    <strong>已选择结果: </strong><span id="selectedResults">无</span>
                </div>
            </div>
            
            <button class="btn btn-warning" onclick="enterResult()">录入结果</button>
        </div>

        <!-- 消息显示区域 -->
        <div class="section">
            <h2>消息记录</h2>
            <div class="messages" id="messages"></div>
        </div>
    </div>

    <script>
        let ws = null;

        function connect() {
            ws = new WebSocket('ws://localhost:8080/ws');
            
            ws.onopen = function() {
                updateStatus(true);
                addMessage('WebSocket连接已建立', 'success');
            };
            
            ws.onmessage = function(event) {
                const message = JSON.parse(event.data);
                addMessage('收到: ' + JSON.stringify(message, null, 2), 'receive');
            };
            
            ws.onerror = function(error) {
                addMessage('WebSocket错误: ' + error, 'error');
            };
            
            ws.onclose = function() {
                updateStatus(false);
                addMessage('WebSocket连接已关闭', 'error');
            };
        }

        function disconnect() {
            if (ws) {
                ws.close();
                ws = null;
            }
        }

        function updateStatus(connected) {
            const indicator = document.getElementById('statusIndicator');
            const statusText = document.getElementById('statusText');
            const connectBtn = document.getElementById('connectBtn');
            const disconnectBtn = document.getElementById('disconnectBtn');
            
            if (connected) {
                indicator.className = 'status-indicator status-connected';
                statusText.textContent = '已连接';
                connectBtn.disabled = true;
                disconnectBtn.disabled = false;
            } else {
                indicator.className = 'status-indicator status-disconnected';
                statusText.textContent = '未连接';
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
            }
        }

        function sendMessage(message) {
            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify(message));
                addMessage('发送: ' + JSON.stringify(message, null, 2), 'send');
            } else {
                addMessage('WebSocket未连接', 'error');
            }
        }

        function addMessage(text, type) {
            const messages = document.getElementById('messages');
            const message = document.createElement('div');
            message.className = 'message message-' + type;
            message.innerHTML = '<span class="timestamp">' + new Date().toLocaleTimeString() + '</span>' + text;
            messages.appendChild(message);
            messages.scrollTop = messages.scrollHeight;
        }

        function clearMessages() {
            document.getElementById('messages').innerHTML = '';
        }

        function login() {
            sendMessage({
                type: 'login',
                data: {
                    username: 'admin',
                    password: '123456'
                }
            });
        }

        function getTableInfo() {
            sendMessage({
                type: 'get_table_info'
            });
        }





        function toggleResult(element) {
            element.classList.toggle('selected');
            updateSelectedResults();
        }

        function updateSelectedResults() {
            const selectedOptions = document.querySelectorAll('.result-option.selected');
            const results = Array.from(selectedOptions).map(option => option.getAttribute('data-result'));
            document.getElementById('selectedResults').textContent = results.length > 0 ? results.join(', ') : '无';
        }

        function enterResult() {
            const tableId = parseInt(document.getElementById('resultTableId').value);
            const accountPeriod = document.getElementById('resultAccountPeriod').value;
            const roundNo = parseInt(document.getElementById('resultRoundNo').value);
            const handNo = parseInt(document.getElementById('resultHandNo').value);
            
            const selectedOptions = document.querySelectorAll('.result-option.selected');
            const results = Array.from(selectedOptions).map(option => option.getAttribute('data-result'));
            
            if (results.length === 0) {
                addMessage('请选择至少一个游戏结果', 'error');
                return;
            }
            
            sendMessage({
                type: 'enter_result',
                data: {
                    table_id: tableId,
                    account_period: accountPeriod,
                    round_no: roundNo,
                    hand_no: handNo,
                    result: results
                }
            });
        }

        // 初始化
        updateStatus(false);
    </script>
</body>
</html> 