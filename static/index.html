<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>记账系统 WebSocket 测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
            font-weight: bold;
        }
        .connected { background-color: #d4edda; color: #155724; }
        .disconnected { background-color: #f8d7da; color: #721c24; }
        .message-area {
            height: 300px;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            overflow-y: auto;
            background-color: #f8f9fa;
            margin-bottom: 20px;
        }
        .input-group {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
        }
        input, select, button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        input[type="text"] {
            flex: 1;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .message {
            margin-bottom: 10px;
            padding: 8px;
            border-radius: 4px;
        }
        .message.sent {
            background-color: #e3f2fd;
            text-align: right;
        }
        .message.received {
            background-color: #f1f8e9;
        }
        .message.system {
            background-color: #fff3cd;
            font-style: italic;
        }
        .timestamp {
            font-size: 0.8em;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>记账系统 WebSocket 测试</h1>
        
        <div id="status" class="status disconnected">
            状态: 未连接
        </div>

        <div class="input-group">
            <input type="text" id="serverUrl" value="ws://192.168.1.12:8080/ws" placeholder="WebSocket服务器地址">
            <button id="connectBtn" onclick="connect()">连接</button>
            <button id="disconnectBtn" onclick="disconnect()" disabled>断开</button>
        </div>

        <div id="messages" class="message-area"></div>

        <div class="input-group">
            <select id="messageType">
                <option value="broadcast">广播消息</option>
                <option value="ping">心跳检测</option>
                <option value="get_clients">获取客户端列表</option>
                <option value="user_info">更新用户信息</option>
            </select>
            <input type="text" id="messageInput" placeholder="输入消息内容" onkeypress="handleEnter(event)">
            <button id="sendBtn" onclick="sendMessage()" disabled>发送</button>
        </div>

        <div class="input-group">
            <button onclick="clearMessages()">清空消息</button>
            <button onclick="getStats()">获取统计</button>
        </div>

        <div style="margin-top: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 5px;">
            <h3>测试页面链接</h3>
            <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                <a href="test_sys_user_api.html" target="_blank" style="padding: 8px 12px; background-color: #007bff; color: white; text-decoration: none; border-radius: 4px;">系统用户API测试</a>
                <a href="test_table_api.html" target="_blank" style="padding: 8px 12px; background-color: #28a745; color: white; text-decoration: none; border-radius: 4px;">桌台API测试</a>
                <a href="test_shuffle_api.html" target="_blank" style="padding: 8px 12px; background-color: #ffc107; color: #212529; text-decoration: none; border-radius: 4px;">洗牌开场API测试</a>
                <a href="test_out_code_api.html" target="_blank" style="padding: 8px 12px; background-color: #17a2b8; color: white; text-decoration: none; border-radius: 4px;">出码申请API测试</a>
                <a href="test_bet_api.html" target="_blank" style="padding: 8px 12px; background-color: #28a745; color: white; text-decoration: none; border-radius: 4px;">下注API测试</a>
                <a href="test_websocket_stability.html" target="_blank" style="padding: 8px 12px; background-color: #6f42c1; color: white; text-decoration: none; border-radius: 4px;">WebSocket稳定性测试</a>
                <a href="quick_bet_test.html" target="_blank" style="padding: 8px 12px; background-color: #fd7e14; color: white; text-decoration: none; border-radius: 4px;">快速批量下注测试</a>
                <a href="test_table_batch_bet.html" target="_blank" style="padding: 8px 12px; background-color: #20c997; color: white; text-decoration: none; border-radius: 4px;">按桌台整体保存批量下注测试</a>
                <a href="test_close_table_api.html" target="_blank" style="padding: 8px 12px; background-color: #dc3545; color: white; text-decoration: none; border-radius: 4px;">收盘API测试</a>
                <a href="test_get_latest_tables_start.html" target="_blank" style="padding: 8px 12px; background-color: #6f42c1; color: white; text-decoration: none; border-radius: 4px;">获取最新开台数据测试</a>
                <a href="test_get_latest_tables_start_optimized.html" target="_blank" style="padding: 8px 12px; background-color: #fd7e14; color: white; text-decoration: none; border-radius: 4px;">获取最新开台数据测试（优化版）</a>
                <a href="test_ip.html" target="_blank" style="padding: 8px 12px; background-color: #e83e8c; color: white; text-decoration: none; border-radius: 4px;">IP地址测试</a>
            </div>
        </div>
    </div>

    <script>
        let ws = null;
        let clientId = null;
        
        const statusEl = document.getElementById('status');
        const messagesEl = document.getElementById('messages');
        const connectBtn = document.getElementById('connectBtn');
        const disconnectBtn = document.getElementById('disconnectBtn');
        const sendBtn = document.getElementById('sendBtn');
        const messageInput = document.getElementById('messageInput');

        function connect() {
            const url = document.getElementById('serverUrl').value;
            
            try {
                ws = new WebSocket(url);
                
                ws.onopen = function(event) {
                    updateStatus('已连接', true);
                    addMessage('系统', '已连接到服务器', 'system');
                    connectBtn.disabled = true;
                    disconnectBtn.disabled = false;
                    sendBtn.disabled = false;
                };
                
                ws.onmessage = function(event) {
                    try {
                        const message = JSON.parse(event.data);
                        handleMessage(message);
                    } catch (e) {
                        addMessage('系统', '收到无效消息: ' + event.data, 'system');
                    }
                };
                
                ws.onclose = function(event) {
                    updateStatus('已断开', false);
                    addMessage('系统', '连接已断开', 'system');
                    connectBtn.disabled = false;
                    disconnectBtn.disabled = true;
                    sendBtn.disabled = true;
                    clientId = null;
                };
                
                ws.onerror = function(error) {
                    addMessage('系统', '连接错误: ' + error, 'system');
                };
                
            } catch (error) {
                addMessage('系统', '连接失败: ' + error.message, 'system');
            }
        }

        function disconnect() {
            if (ws) {
                ws.close();
            }
        }

        function sendMessage() {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                addMessage('系统', '未连接到服务器', 'system');
                return;
            }

            const type = document.getElementById('messageType').value;
            const content = messageInput.value.trim();
            
            let message = {
                type: type,
                timestamp: Date.now()
            };

            switch (type) {
                case 'broadcast':
                    if (!content) {
                        alert('请输入广播消息内容');
                        return;
                    }
                    message.data = content;
                    break;
                case 'ping':
                    message.data = 'ping';
                    break;
                case 'get_clients':
                    // 不需要额外数据
                    break;
                case 'user_info':
                    if (!content) {
                        alert('请输入用户信息 (格式: user_id:username)');
                        return;
                    }
                    const parts = content.split(':');
                    message.data = {
                        user_id: parts[0] || '',
                        username: parts[1] || ''
                    };
                    break;
            }

            ws.send(JSON.stringify(message));
            addMessage('我', JSON.stringify(message, null, 2), 'sent');
            messageInput.value = '';
        }

        function handleMessage(message) {
            let displayMessage = '';
            
            switch (message.type) {
                case 'connection':
                    if (message.data && message.data.id) {
                        clientId = message.data.id;
                        displayMessage = `连接成功，客户端ID: ${clientId}`;
                    } else {
                        displayMessage = JSON.stringify(message.data);
                    }
                    break;
                case 'pong':
                    displayMessage = 'Pong 响应';
                    break;
                case 'clients_list':
                    displayMessage = `客户端列表: ${JSON.stringify(message.data, null, 2)}`;
                    break;
                case 'broadcast':
                    displayMessage = `广播消息 (来自 ${message.from}): ${JSON.stringify(message.data)}`;
                    break;
                default:
                    displayMessage = JSON.stringify(message, null, 2);
            }
            
            addMessage('服务器', displayMessage, 'received');
        }

        function addMessage(sender, content, type = 'received') {
            const messageEl = document.createElement('div');
            messageEl.className = `message ${type}`;
            
            const timestamp = new Date().toLocaleTimeString();
            messageEl.innerHTML = `
                <div><strong>${sender}:</strong></div>
                <div>${content}</div>
                <div class="timestamp">${timestamp}</div>
            `;
            
            messagesEl.appendChild(messageEl);
            messagesEl.scrollTop = messagesEl.scrollHeight;
        }

        function updateStatus(text, connected) {
            statusEl.textContent = `状态: ${text}`;
            statusEl.className = `status ${connected ? 'connected' : 'disconnected'}`;
        }

        function clearMessages() {
            messagesEl.innerHTML = '';
        }

        function handleEnter(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        async function getStats() {
            try {
                const response = await fetch('/api/ws/stats');
                const data = await response.json();
                addMessage('系统', `统计信息: ${JSON.stringify(data, null, 2)}`, 'system');
            } catch (error) {
                addMessage('系统', `获取统计信息失败: ${error.message}`, 'system');
            }
        }

        // 页面加载时自动连接
        window.addEventListener('load', function() {
            // 可以在这里自动连接
            // connect();
        });
    </script>
</body>
</html> 