<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>查询最新开台数据测试（优化版）</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, button {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            cursor: pointer;
            margin-top: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        .status.connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.authenticated {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .messages {
            height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background-color: #f8f9fa;
            font-family: monospace;
            font-size: 12px;
        }
        .message {
            margin-bottom: 10px;
            padding: 5px;
            border-radius: 3px;
        }
        .message.send {
            background-color: #e3f2fd;
            border-left: 3px solid #2196f3;
        }
        .message.receive {
            background-color: #f3e5f5;
            border-left: 3px solid #9c27b0;
        }
        .message.success {
            background-color: #e8f5e8;
            border-left: 3px solid #4caf50;
        }
        .message.error {
            background-color: #ffebee;
            border-left: 3px solid #f44336;
        }
        .result-container {
            display: none;
            margin-top: 20px;
        }
        .result-data {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .feature-highlight {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .feature-highlight h3 {
            margin-top: 0;
            color: #856404;
        }
    </style>
</head>
<body>
    <h1>查询最新开台数据测试（优化版）</h1>
    
    <div class="feature-highlight">
        <h3>🚀 功能优化说明</h3>
        <p><strong>新增功能：</strong>通过客户端IP自动查询最新开台数据，无需手动输入桌台编号</p>
        <p><strong>优化特性：</strong></p>
        <ul>
            <li>✅ 获取桌台信息时自动缓存到Redis（1小时有效期）</li>
            <li>✅ 查询开台数据时优先从Redis获取桌台信息，提升性能</li>
            <li>✅ 无需前端传参，系统自动根据IP识别桌台</li>
            <li>✅ 兼容原有的桌台编号查询方式</li>
        </ul>
    </div>

    <div class="two-column">
        <div class="container">
            <h2>连接控制</h2>
            <div id="connectionStatus" class="status disconnected">未连接</div>
            <div id="authStatus" class="status disconnected">未认证</div>
            
            <button id="connectBtn" onclick="connect()">连接WebSocket</button>
            <button id="disconnectBtn" onclick="disconnect()" disabled>断开连接</button>
            
            <div class="form-group">
                <label for="username">用户名:</label>
                <input type="text" id="username" value="admin" placeholder="请输入用户名">
            </div>
            <div class="form-group">
                <label for="password">密码:</label>
                <input type="password" id="password" value="123456" placeholder="请输入密码">
            </div>
            <button id="loginBtn" onclick="login()" disabled>登录</button>
            <button id="logoutBtn" onclick="logout()" disabled>登出</button>
        </div>

        <div class="container">
            <h2>功能测试</h2>
            
            <h3>1. 优化版查询（推荐）</h3>
            <p>无需传参，自动根据客户端IP查询最新开台数据</p>
            <button id="getLatestBtn" onclick="getLatestTablesStart()" disabled>查询最新开台数据</button>
            
            <h3>2. 桌台信息查询</h3>
            <p>查询桌台信息并缓存到Redis</p>
            <button id="getTableInfoBtn" onclick="getTableInfo()" disabled>获取桌台信息</button>
            

        </div>
    </div>

    <div class="container">
        <h2>开台数据结果</h2>
        <div id="tablesStartResult" class="result-container">
            <pre id="tablesStartData" class="result-data"></pre>
        </div>
    </div>

    <div class="container">
        <h2>消息日志</h2>
        <button onclick="clearMessages()">清空消息</button>
        <div id="messages" class="messages"></div>
    </div>

    <script>
        let ws = null;
        let isConnected = false;
        let isAuthenticated = false;
        
        // 获取页面元素
        const connectionStatus = document.getElementById('connectionStatus');
        const authStatus = document.getElementById('authStatus');
        const connectBtn = document.getElementById('connectBtn');
        const disconnectBtn = document.getElementById('disconnectBtn');
        const loginBtn = document.getElementById('loginBtn');
        const logoutBtn = document.getElementById('logoutBtn');
        const getLatestBtn = document.getElementById('getLatestBtn');
        const getLatestByCodeBtn = document.getElementById('getLatestByCodeBtn');
        const getTableInfoBtn = document.getElementById('getTableInfoBtn');
        const messages = document.getElementById('messages');
        const tablesStartResult = document.getElementById('tablesStartResult');
        const tablesStartData = document.getElementById('tablesStartData');

        // 更新连接状态
        function updateConnectionStatus(connected) {
            isConnected = connected;
            if (connected) {
                connectionStatus.textContent = '已连接';
                connectionStatus.className = 'status connected';
                connectBtn.disabled = true;
                disconnectBtn.disabled = false;
                loginBtn.disabled = false;
            } else {
                connectionStatus.textContent = '未连接';
                connectionStatus.className = 'status disconnected';
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
                loginBtn.disabled = true;
                updateAuthStatus(false);
            }
        }

        // 更新认证状态
        function updateAuthStatus(authenticated) {
            isAuthenticated = authenticated;
            if (authenticated) {
                authStatus.textContent = '已认证';
                authStatus.className = 'status authenticated';
                logoutBtn.disabled = false;
                getLatestBtn.disabled = false;
                getTableInfoBtn.disabled = false;
            } else {
                authStatus.textContent = '未认证';
                authStatus.className = 'status disconnected';
                logoutBtn.disabled = true;
                getLatestBtn.disabled = true;
                getTableInfoBtn.disabled = true;
            }
        }

        // 添加消息到日志
        function addMessage(message, type = 'receive') {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.innerHTML = `<strong>[${new Date().toLocaleTimeString()}]</strong> ${message}`;
            messages.appendChild(messageDiv);
            messages.scrollTop = messages.scrollHeight;
        }

        // 连接WebSocket
        function connect() {
            try {
                ws = new WebSocket('ws://localhost:8080/ws');
                
                ws.onopen = function(event) {
                    updateConnectionStatus(true);
                    addMessage('WebSocket连接成功', 'success');
                };
                
                ws.onmessage = function(event) {
                    const message = JSON.parse(event.data);
                    addMessage('收到消息: ' + JSON.stringify(message, null, 2), 'receive');
                    handleMessage(message);
                };
                
                ws.onclose = function(event) {
                    updateConnectionStatus(false);
                    addMessage('WebSocket连接关闭', 'error');
                };
                
                ws.onerror = function(error) {
                    addMessage('WebSocket错误: ' + error, 'error');
                };
            } catch (error) {
                addMessage('连接失败: ' + error.message, 'error');
            }
        }

        // 断开连接
        function disconnect() {
            if (ws) {
                ws.close();
            }
        }

        // 处理接收到的消息
        function handleMessage(message) {
            switch (message.type) {
                case 'login_success':
                    updateAuthStatus(true);
                    addMessage('登录成功', 'success');
                    break;
                case 'login_error':
                    addMessage('登录失败: ' + message.data.error, 'error');
                    break;
                case 'logout_success':
                    updateAuthStatus(false);
                    addMessage('登出成功', 'success');
                    break;
                case 'table_info_success':
                    addMessage('桌台信息获取成功，已缓存到Redis', 'success');
                    displayResult('桌台信息', message.data);
                    break;
                case 'table_info_error':
                    addMessage('桌台信息获取失败: ' + message.data.error, 'error');
                    break;
                case 'get_latest_tables_start_success':
                    addMessage('查询最新开台数据成功（优化版）', 'success');
                    displayResult('最新开台数据', message.data);
                    break;
                case 'get_latest_tables_start_error':
                    addMessage('查询最新开台数据失败: ' + message.data.error, 'error');
                    tablesStartResult.style.display = 'none';
                    break;
            }
        }

        // 显示结果数据
        function displayResult(title, data) {
            tablesStartData.textContent = `=== ${title} ===\n\n` + JSON.stringify(data, null, 2);
            tablesStartResult.style.display = 'block';
        }

        // 发送消息
        function sendMessage(message) {
            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify(message));
                addMessage('发送消息: ' + JSON.stringify(message, null, 2), 'send');
            } else {
                addMessage('WebSocket未连接', 'error');
            }
        }

        // 用户登录
        function login() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            if (!username || !password) {
                addMessage('请输入用户名和密码', 'error');
                return;
            }

            sendMessage({
                type: 'login',
                data: {
                    username: username,
                    password: password
                }
            });
        }

        // 用户登出
        function logout() {
            sendMessage({
                type: 'logout'
            });
        }

        // 获取桌台信息（会缓存到Redis）
        function getTableInfo() {
            sendMessage({
                type: 'get_table_info'
            });
        }

        // 查询最新开台数据（优化版，无需传参）
        function getLatestTablesStart() {
            sendMessage({
                type: 'get_latest_tables_start'
            });
        }



        // 清空消息
        function clearMessages() {
            messages.innerHTML = '';
        }

        // 页面加载完成后的提示
        window.addEventListener('load', function() {
            addMessage('页面加载完成，点击"连接WebSocket"开始测试', 'success');
            addMessage('功能说明：系统会自动根据客户端IP获取桌台信息，并缓存到Redis', 'success');
        });
    </script>
</body>
</html>
