package models

import (
	"time"
)

// TablesStart 桌台新开账期表
type TablesStart struct {
	ID            int64     `json:"id" gorm:"primaryKey;autoIncrement;comment:主键ID"`
	TableID       int64     `json:"table_id" gorm:"not null;comment:桌台ID"`
	TablesName    string    `json:"tables_name" gorm:"type:varchar(32);not null;comment:桌台名称"`
	AccountPeriod string    `json:"account_period" gorm:"type:varchar(32);not null;default:'';comment:账期"`
	GameType      int8      `json:"game_type" gorm:"type:tinyint(1);comment:游戏类型:1-百家乐;2-龙虎斗;3-百家乐免佣;4-牛牛;5-三公;6-A89;7-庄闲牛;8-骰宝;9-轮盘"`
	Stats         int8      `json:"stats" gorm:"type:tinyint(1);not null;default:2;comment:当前状态:1-销售中;2-等待洗牌;3-等待出码;4-已收盘"`
	ShoeNo        int       `json:"shoe_no" gorm:"not null;comment:场次编号"`
	HandNo        int       `json:"hand_no" gorm:"not null;comment:局号编号"`
	CreateTime    time.Time `json:"create_time" gorm:"default:CURRENT_TIMESTAMP;comment:创建时间"`
}

// TableName 指定表名
func (TablesStart) TableName() string {
	return "tables_start"
}
