package models

import (
	"time"
)

// SettlementRecord 点码记录
type SettlementRecord struct {
	ID            int       `json:"id" gorm:"primaryKey;autoIncrement;comment:主键ID"`
	TableID       int       `json:"table_id" gorm:"not null;comment:桌台ID"`
	TablesName    string    `json:"tables_name" gorm:"type:varchar(32);not null;comment:桌台名称"`
	ShoeNo        int       `json:"shoe_no" gorm:"not null;comment:场次编号"`
	AccountPeriod string    `json:"account_period" gorm:"type:varchar(32);default:'';not null;comment:账期"`
	ChipsOutput   float64   `json:"chips_output" gorm:"type:decimal(12,2);default:0.00;comment:出码量"`
	ChipsAdd      float64   `json:"chips_add" gorm:"type:decimal(12,2);default:0.00;comment:加彩量"`
	CurrencyType  int8      `json:"currency_type" gorm:"type:tinyint(1);comment:货币类型:1-筹码;2-现金;3-U码;"`
	Chips20W      int       `json:"chips_20w" gorm:"default:0;not null;comment:筹码20万"`
	Chips10W      int       `json:"chips_10w" gorm:"default:0;not null;comment:筹码10万"`
	Chips5W       int       `json:"chips_5w" gorm:"default:0;not null;comment:筹码5万"`
	Chips1W       int       `json:"chips_1w" gorm:"default:0;not null;comment:筹码1万"`
	Chips5K       int       `json:"chips_5k" gorm:"default:0;not null;comment:筹码5千"`
	Chips1K       int       `json:"chips_1k" gorm:"default:0;not null;comment:筹码1千"`
	Chips500      int       `json:"chips_500" gorm:"default:0;not null;comment:筹码500"`
	Chips100      int       `json:"chips_100" gorm:"default:0;not null;comment:筹码100"`
	Chips50       int       `json:"chips_50" gorm:"default:0;not null;comment:筹码50"`
	Chips10       int       `json:"chips_10" gorm:"default:0;not null;comment:筹码10"`
	Chips5        int       `json:"chips_5" gorm:"default:0;not null;comment:筹码5"`
	TotalAmount   float64   `json:"total_amount" gorm:"type:decimal(12,2);default:0.00;comment:总额"`
	ClientWinLoss float64   `json:"client_win_loss" gorm:"type:decimal(12,2);default:0.00;comment:客户输赢总和"`
	AmountTip     float64   `json:"amount_tip" gorm:"type:decimal(12,2);default:0.00;comment:小费金额"`
	AmountBottom  float64   `json:"amount_bottom" gorm:"type:decimal(12,2);default:0.00;comment:和底"`
	WashRate      float64   `json:"wash_rate" gorm:"type:decimal(5,4);comment:洗码率"`
	WashAmount    float64   `json:"wash_amount" gorm:"type:decimal(12,2);default:0.00;comment:本局洗码量"`
	WashTip       float64   `json:"wash_tip" gorm:"type:decimal(12,2);default:0.00;comment:洗码费"`
	CompareResult string    `json:"compare_result" gorm:"type:varchar(255);comment:点码与注单比对结果"`
	Remark        string    `json:"remark" gorm:"type:text;comment:备注"`
	Operator      string    `json:"operator" gorm:"type:varchar(64);comment:操作人员"`
	CreateTime    time.Time `json:"create_time" gorm:"default:CURRENT_TIMESTAMP;comment:创建时间"`
}

// TableName 设置表名
func (SettlementRecord) TableName() string {
	return "settlement_records"
}
